// Generated by view binder compiler. Do not edit!
package com.quantumhealing.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.quantumhealing.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AutoCompleteTextView actvDay;

  @NonNull
  public final AutoCompleteTextView actvDisease;

  @NonNull
  public final AutoCompleteTextView actvGender;

  @NonNull
  public final AutoCompleteTextView actvHour;

  @NonNull
  public final AutoCompleteTextView actvHourPillar;

  @NonNull
  public final AutoCompleteTextView actvLocation;

  @NonNull
  public final AutoCompleteTextView actvMinute;

  @NonNull
  public final AutoCompleteTextView actvMonth;

  @NonNull
  public final AutoCompleteTextView actvYear;

  @NonNull
  public final AutoCompleteTextView actvYearPillar;

  @NonNull
  public final MaterialButton btnAdd;

  @NonNull
  public final MaterialButton btnDelete;

  @NonNull
  public final MaterialButton btnStartSystem;

  @NonNull
  public final MaterialButton btnUpdate;

  @NonNull
  public final TextInputEditText etName;

  @NonNull
  public final RecyclerView rvPersons;

  @NonNull
  public final TextView tvStatus;

  private ActivityMainBinding(@NonNull LinearLayout rootView, @NonNull AutoCompleteTextView actvDay,
      @NonNull AutoCompleteTextView actvDisease, @NonNull AutoCompleteTextView actvGender,
      @NonNull AutoCompleteTextView actvHour, @NonNull AutoCompleteTextView actvHourPillar,
      @NonNull AutoCompleteTextView actvLocation, @NonNull AutoCompleteTextView actvMinute,
      @NonNull AutoCompleteTextView actvMonth, @NonNull AutoCompleteTextView actvYear,
      @NonNull AutoCompleteTextView actvYearPillar, @NonNull MaterialButton btnAdd,
      @NonNull MaterialButton btnDelete, @NonNull MaterialButton btnStartSystem,
      @NonNull MaterialButton btnUpdate, @NonNull TextInputEditText etName,
      @NonNull RecyclerView rvPersons, @NonNull TextView tvStatus) {
    this.rootView = rootView;
    this.actvDay = actvDay;
    this.actvDisease = actvDisease;
    this.actvGender = actvGender;
    this.actvHour = actvHour;
    this.actvHourPillar = actvHourPillar;
    this.actvLocation = actvLocation;
    this.actvMinute = actvMinute;
    this.actvMonth = actvMonth;
    this.actvYear = actvYear;
    this.actvYearPillar = actvYearPillar;
    this.btnAdd = btnAdd;
    this.btnDelete = btnDelete;
    this.btnStartSystem = btnStartSystem;
    this.btnUpdate = btnUpdate;
    this.etName = etName;
    this.rvPersons = rvPersons;
    this.tvStatus = tvStatus;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.actvDay;
      AutoCompleteTextView actvDay = ViewBindings.findChildViewById(rootView, id);
      if (actvDay == null) {
        break missingId;
      }

      id = R.id.actvDisease;
      AutoCompleteTextView actvDisease = ViewBindings.findChildViewById(rootView, id);
      if (actvDisease == null) {
        break missingId;
      }

      id = R.id.actvGender;
      AutoCompleteTextView actvGender = ViewBindings.findChildViewById(rootView, id);
      if (actvGender == null) {
        break missingId;
      }

      id = R.id.actvHour;
      AutoCompleteTextView actvHour = ViewBindings.findChildViewById(rootView, id);
      if (actvHour == null) {
        break missingId;
      }

      id = R.id.actvHourPillar;
      AutoCompleteTextView actvHourPillar = ViewBindings.findChildViewById(rootView, id);
      if (actvHourPillar == null) {
        break missingId;
      }

      id = R.id.actvLocation;
      AutoCompleteTextView actvLocation = ViewBindings.findChildViewById(rootView, id);
      if (actvLocation == null) {
        break missingId;
      }

      id = R.id.actvMinute;
      AutoCompleteTextView actvMinute = ViewBindings.findChildViewById(rootView, id);
      if (actvMinute == null) {
        break missingId;
      }

      id = R.id.actvMonth;
      AutoCompleteTextView actvMonth = ViewBindings.findChildViewById(rootView, id);
      if (actvMonth == null) {
        break missingId;
      }

      id = R.id.actvYear;
      AutoCompleteTextView actvYear = ViewBindings.findChildViewById(rootView, id);
      if (actvYear == null) {
        break missingId;
      }

      id = R.id.actvYearPillar;
      AutoCompleteTextView actvYearPillar = ViewBindings.findChildViewById(rootView, id);
      if (actvYearPillar == null) {
        break missingId;
      }

      id = R.id.btnAdd;
      MaterialButton btnAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnAdd == null) {
        break missingId;
      }

      id = R.id.btnDelete;
      MaterialButton btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.btnStartSystem;
      MaterialButton btnStartSystem = ViewBindings.findChildViewById(rootView, id);
      if (btnStartSystem == null) {
        break missingId;
      }

      id = R.id.btnUpdate;
      MaterialButton btnUpdate = ViewBindings.findChildViewById(rootView, id);
      if (btnUpdate == null) {
        break missingId;
      }

      id = R.id.etName;
      TextInputEditText etName = ViewBindings.findChildViewById(rootView, id);
      if (etName == null) {
        break missingId;
      }

      id = R.id.rvPersons;
      RecyclerView rvPersons = ViewBindings.findChildViewById(rootView, id);
      if (rvPersons == null) {
        break missingId;
      }

      id = R.id.tvStatus;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      return new ActivityMainBinding((LinearLayout) rootView, actvDay, actvDisease, actvGender,
          actvHour, actvHourPillar, actvLocation, actvMinute, actvMonth, actvYear, actvYearPillar,
          btnAdd, btnDelete, btnStartSystem, btnUpdate, etName, rvPersons, tvStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
