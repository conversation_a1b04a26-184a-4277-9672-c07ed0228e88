// Generated by view binder compiler. Do not edit!
package com.quantumhealing.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.quantumhealing.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnAdd;

  @NonNull
  public final Button btnDelete;

  @NonNull
  public final Button btnStartSystem;

  @NonNull
  public final Button btnUpdate;

  @NonNull
  public final TextInputEditText etName;

  @NonNull
  public final RecyclerView rvPersons;

  @NonNull
  public final Spinner spinnerDay;

  @NonNull
  public final Spinner spinnerDisease;

  @NonNull
  public final Spinner spinnerGender;

  @NonNull
  public final Spinner spinnerHour;

  @NonNull
  public final Spinner spinnerHourPillar;

  @NonNull
  public final Spinner spinnerLocation;

  @NonNull
  public final Spinner spinnerMinute;

  @NonNull
  public final Spinner spinnerMonth;

  @NonNull
  public final Spinner spinnerYear;

  @NonNull
  public final Spinner spinnerYearPillar;

  @NonNull
  public final TextView tvStatus;

  private ActivityMainBinding(@NonNull LinearLayout rootView, @NonNull Button btnAdd,
      @NonNull Button btnDelete, @NonNull Button btnStartSystem, @NonNull Button btnUpdate,
      @NonNull TextInputEditText etName, @NonNull RecyclerView rvPersons,
      @NonNull Spinner spinnerDay, @NonNull Spinner spinnerDisease, @NonNull Spinner spinnerGender,
      @NonNull Spinner spinnerHour, @NonNull Spinner spinnerHourPillar,
      @NonNull Spinner spinnerLocation, @NonNull Spinner spinnerMinute,
      @NonNull Spinner spinnerMonth, @NonNull Spinner spinnerYear,
      @NonNull Spinner spinnerYearPillar, @NonNull TextView tvStatus) {
    this.rootView = rootView;
    this.btnAdd = btnAdd;
    this.btnDelete = btnDelete;
    this.btnStartSystem = btnStartSystem;
    this.btnUpdate = btnUpdate;
    this.etName = etName;
    this.rvPersons = rvPersons;
    this.spinnerDay = spinnerDay;
    this.spinnerDisease = spinnerDisease;
    this.spinnerGender = spinnerGender;
    this.spinnerHour = spinnerHour;
    this.spinnerHourPillar = spinnerHourPillar;
    this.spinnerLocation = spinnerLocation;
    this.spinnerMinute = spinnerMinute;
    this.spinnerMonth = spinnerMonth;
    this.spinnerYear = spinnerYear;
    this.spinnerYearPillar = spinnerYearPillar;
    this.tvStatus = tvStatus;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAdd;
      Button btnAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnAdd == null) {
        break missingId;
      }

      id = R.id.btnDelete;
      Button btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.btnStartSystem;
      Button btnStartSystem = ViewBindings.findChildViewById(rootView, id);
      if (btnStartSystem == null) {
        break missingId;
      }

      id = R.id.btnUpdate;
      Button btnUpdate = ViewBindings.findChildViewById(rootView, id);
      if (btnUpdate == null) {
        break missingId;
      }

      id = R.id.etName;
      TextInputEditText etName = ViewBindings.findChildViewById(rootView, id);
      if (etName == null) {
        break missingId;
      }

      id = R.id.rvPersons;
      RecyclerView rvPersons = ViewBindings.findChildViewById(rootView, id);
      if (rvPersons == null) {
        break missingId;
      }

      id = R.id.spinnerDay;
      Spinner spinnerDay = ViewBindings.findChildViewById(rootView, id);
      if (spinnerDay == null) {
        break missingId;
      }

      id = R.id.spinnerDisease;
      Spinner spinnerDisease = ViewBindings.findChildViewById(rootView, id);
      if (spinnerDisease == null) {
        break missingId;
      }

      id = R.id.spinnerGender;
      Spinner spinnerGender = ViewBindings.findChildViewById(rootView, id);
      if (spinnerGender == null) {
        break missingId;
      }

      id = R.id.spinnerHour;
      Spinner spinnerHour = ViewBindings.findChildViewById(rootView, id);
      if (spinnerHour == null) {
        break missingId;
      }

      id = R.id.spinnerHourPillar;
      Spinner spinnerHourPillar = ViewBindings.findChildViewById(rootView, id);
      if (spinnerHourPillar == null) {
        break missingId;
      }

      id = R.id.spinnerLocation;
      Spinner spinnerLocation = ViewBindings.findChildViewById(rootView, id);
      if (spinnerLocation == null) {
        break missingId;
      }

      id = R.id.spinnerMinute;
      Spinner spinnerMinute = ViewBindings.findChildViewById(rootView, id);
      if (spinnerMinute == null) {
        break missingId;
      }

      id = R.id.spinnerMonth;
      Spinner spinnerMonth = ViewBindings.findChildViewById(rootView, id);
      if (spinnerMonth == null) {
        break missingId;
      }

      id = R.id.spinnerYear;
      Spinner spinnerYear = ViewBindings.findChildViewById(rootView, id);
      if (spinnerYear == null) {
        break missingId;
      }

      id = R.id.spinnerYearPillar;
      Spinner spinnerYearPillar = ViewBindings.findChildViewById(rootView, id);
      if (spinnerYearPillar == null) {
        break missingId;
      }

      id = R.id.tvStatus;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      return new ActivityMainBinding((LinearLayout) rootView, btnAdd, btnDelete, btnStartSystem,
          btnUpdate, etName, rvPersons, spinnerDay, spinnerDisease, spinnerGender, spinnerHour,
          spinnerHourPillar, spinnerLocation, spinnerMinute, spinnerMonth, spinnerYear,
          spinnerYearPillar, tvStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
