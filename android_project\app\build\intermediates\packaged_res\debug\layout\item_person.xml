<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- 状态指示器 -->
        <View
            android:id="@+id/vStatusIndicator"
            android:layout_width="8dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="12dp"
            android:background="@color/design_default_color_surface" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="姓名"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvGender"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="性别"
                    android:textSize="14sp"
                    android:layout_marginStart="8dp" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvDisease"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="疾病"
                android:textSize="14sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/tvBirthInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="出生信息"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="2dp" />
        </LinearLayout>

        <!-- 激活开关 -->
        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/swActive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp" />
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
