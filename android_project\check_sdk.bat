@echo off
echo 检查已安装的 Android SDK 版本...
echo.

REM 检查常见的 Android SDK 路径
set SDK_PATHS[0]=%LOCALAPPDATA%\Android\Sdk
set SDK_PATHS[1]=%USERPROFILE%\AppData\Local\Android\Sdk
set SDK_PATHS[2]=C:\Users\<USER>\AppData\Local\Android\Sdk
set SDK_PATHS[3]=C:\Android\Sdk

for /L %%i in (0,1,3) do (
    call set SDK_PATH=%%SDK_PATHS[%%i]%%
    if exist "!SDK_PATH!\platforms" (
        echo 找到 Android SDK 路径: !SDK_PATH!
        echo.
        echo 已安装的 Platform SDK 版本:
        dir "!SDK_PATH!\platforms" /B | findstr android-
        echo.
        echo 已安装的 Build Tools 版本:
        if exist "!SDK_PATH!\build-tools" (
            dir "!SDK_PATH!\build-tools" /B
        ) else (
            echo 未找到 build-tools 目录
        )
        goto :found
    )
)

echo 未找到 Android SDK 安装路径
echo 请检查 Android Studio 是否正确安装
echo.
echo 常见安装路径:
echo - %LOCALAPPDATA%\Android\Sdk
echo - C:\Users\<USER>\AppData\Local\Android\Sdk
goto :end

:found
echo.
echo 建议操作:
echo 1. 如果看到 android-31 或更高版本，项目应该可以编译
echo 2. 如果只有较低版本，请在 Android Studio 中安装 API 31+
echo 3. 或者告诉我您的最高版本，我会调整项目配置

:end
pause
