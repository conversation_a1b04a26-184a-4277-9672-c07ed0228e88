// Generated by view binder compiler. Do not edit!
package com.quantumhealing.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.quantumhealing.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPersonBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final SwitchMaterial swActive;

  @NonNull
  public final TextView tvBirthInfo;

  @NonNull
  public final TextView tvDisease;

  @NonNull
  public final TextView tvGender;

  @NonNull
  public final TextView tvName;

  @NonNull
  public final View vStatusIndicator;

  private ItemPersonBinding(@NonNull MaterialCardView rootView, @NonNull SwitchMaterial swActive,
      @NonNull TextView tvBirthInfo, @NonNull TextView tvDisease, @NonNull TextView tvGender,
      @NonNull TextView tvName, @NonNull View vStatusIndicator) {
    this.rootView = rootView;
    this.swActive = swActive;
    this.tvBirthInfo = tvBirthInfo;
    this.tvDisease = tvDisease;
    this.tvGender = tvGender;
    this.tvName = tvName;
    this.vStatusIndicator = vStatusIndicator;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPersonBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPersonBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_person, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPersonBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.swActive;
      SwitchMaterial swActive = ViewBindings.findChildViewById(rootView, id);
      if (swActive == null) {
        break missingId;
      }

      id = R.id.tvBirthInfo;
      TextView tvBirthInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvBirthInfo == null) {
        break missingId;
      }

      id = R.id.tvDisease;
      TextView tvDisease = ViewBindings.findChildViewById(rootView, id);
      if (tvDisease == null) {
        break missingId;
      }

      id = R.id.tvGender;
      TextView tvGender = ViewBindings.findChildViewById(rootView, id);
      if (tvGender == null) {
        break missingId;
      }

      id = R.id.tvName;
      TextView tvName = ViewBindings.findChildViewById(rootView, id);
      if (tvName == null) {
        break missingId;
      }

      id = R.id.vStatusIndicator;
      View vStatusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (vStatusIndicator == null) {
        break missingId;
      }

      return new ItemPersonBinding((MaterialCardView) rootView, swActive, tvBirthInfo, tvDisease,
          tvGender, tvName, vStatusIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
