package com.quantumhealing.app.repository

import androidx.lifecycle.LiveData
import com.quantumhealing.app.data.dao.PersonDao
import com.quantumhealing.app.data.model.Person

/**
 * 人员数据仓库
 */
class PersonRepository(private val personDao: PersonDao) {
    
    val allPersons: LiveData<List<Person>> = personDao.getAllPersons()
    
    suspend fun insert(person: Person): Long {
        return personDao.insertPerson(person)
    }
    
    suspend fun update(person: Person) {
        personDao.updatePerson(person)
    }
    
    suspend fun delete(person: Person) {
        personDao.deletePerson(person)
    }
    
    suspend fun getPersonByName(name: String): Person? {
        return personDao.getPersonByName(name)
    }
    
    suspend fun getActivePersons(): List<Person> {
        return personDao.getActivePersons()
    }
    
    suspend fun getActivePersonCount(): Int {
        return personDao.getActivePersonCount()
    }
    
    suspend fun updatePersonActiveStatus(id: Long, isActive: Boolean) {
        personDao.updatePersonActiveStatus(id, isActive)
    }
}
