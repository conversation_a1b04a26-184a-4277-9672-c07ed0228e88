{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-35:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bea0caf4c4157f01bbd28088de62123\\transformed\\appcompat-1.5.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "271,377,476,587,673,775,892,973,1050,1142,1236,1332,1434,1543,1637,1738,1832,1924,2017,2100,2211,2315,2414,2524,2626,2725,2891,7310", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "372,471,582,668,770,887,968,1045,1137,1231,1327,1429,1538,1632,1733,1827,1919,2012,2095,2206,2310,2409,2519,2621,2720,2886,2988,7388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d0cd04beb3e7b9a1429c24c8d7d0af4\\transformed\\core-1.8.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7393", "endColumns": "100", "endOffsets": "7489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,221,306,405,522,604,668,753,821,885,972,1036,1095,1167,1231,1285,1404,1464,1525,1579,1652,1785,1869,1962,2070,2150,2229,2317,2384,2450,2523,2602,2688,2761,2836,2910,2982,3070,3147,3238,3330,3402,3476,3567,3621,3690,3773,3859,3921,3985,4048,4151,4255,4352,4457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,98,116,81,63,84,67,63,86,63,58,71,63,53,118,59,60,53,72,132,83,92,107,79,78,87,66,65,72,78,85,72,74,73,71,87,76,90,91,71,73,90,53,68,82,85,61,63,62,102,103,96,104,80", "endOffsets": "216,301,400,517,599,663,748,816,880,967,1031,1090,1162,1226,1280,1399,1459,1520,1574,1647,1780,1864,1957,2065,2145,2224,2312,2379,2445,2518,2597,2683,2756,2831,2905,2977,3065,3142,3233,3325,3397,3471,3562,3616,3685,3768,3854,3916,3980,4043,4146,4250,4347,4452,4533"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2993,3078,3177,3294,3376,3440,3525,3593,3657,3744,3808,3867,3939,4003,4057,4176,4236,4297,4351,4424,4557,4641,4734,4842,4922,5001,5089,5156,5222,5295,5374,5460,5533,5608,5682,5754,5842,5919,6010,6102,6174,6248,6339,6393,6462,6545,6631,6693,6757,6820,6923,7027,7124,7229", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,84,98,116,81,63,84,67,63,86,63,58,71,63,53,118,59,60,53,72,132,83,92,107,79,78,87,66,65,72,78,85,72,74,73,71,87,76,90,91,71,73,90,53,68,82,85,61,63,62,102,103,96,104,80", "endOffsets": "266,3073,3172,3289,3371,3435,3520,3588,3652,3739,3803,3862,3934,3998,4052,4171,4231,4292,4346,4419,4552,4636,4729,4837,4917,4996,5084,5151,5217,5290,5369,5455,5528,5603,5677,5749,5837,5914,6005,6097,6169,6243,6334,6388,6457,6540,6626,6688,6752,6815,6918,7022,7119,7224,7305"}}]}]}