#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android 开发环境检测工具
检测当前系统的 Android SDK、JDK、Gradle 等环境信息
"""

import os
import sys
import json
import subprocess
import platform
from pathlib import Path

class AndroidEnvironmentDetector:
    def __init__(self):
        self.system = platform.system()
        self.environment_info = {
            "system": self.system,
            "android_sdk": None,
            "jdk_version": None,
            "gradle_version": None,
            "android_studio": None,
            "available_apis": [],
            "build_tools": [],
            "recommended_config": {}
        }
    
    def detect_android_sdk(self):
        """检测 Android SDK 路径和版本"""
        possible_paths = []
        
        if self.system == "Windows":
            user_home = os.path.expanduser("~")
            possible_paths = [
                os.path.join(user_home, "AppData", "Local", "Android", "Sdk"),
                os.path.join("C:", "Android", "Sdk"),
                os.path.join("C:", "Users", os.getenv("USERNAME", ""), "AppData", "Local", "Android", "Sdk")
            ]
        elif self.system == "Darwin":  # macOS
            user_home = os.path.expanduser("~")
            possible_paths = [
                os.path.join(user_home, "Library", "Android", "sdk"),
                os.path.join(user_home, "Android", "Sdk")
            ]
        else:  # Linux
            user_home = os.path.expanduser("~")
            possible_paths = [
                os.path.join(user_home, "Android", "Sdk"),
                os.path.join("/opt", "android-sdk")
            ]
        
        # 检查环境变量
        android_home = os.getenv("ANDROID_HOME")
        if android_home:
            possible_paths.insert(0, android_home)
        
        android_sdk_root = os.getenv("ANDROID_SDK_ROOT")
        if android_sdk_root:
            possible_paths.insert(0, android_sdk_root)
        
        for path in possible_paths:
            if os.path.exists(path) and os.path.exists(os.path.join(path, "platforms")):
                self.environment_info["android_sdk"] = path
                self._detect_api_levels(path)
                self._detect_build_tools(path)
                break
    
    def _detect_api_levels(self, sdk_path):
        """检测可用的 API 级别"""
        platforms_path = os.path.join(sdk_path, "platforms")
        if os.path.exists(platforms_path):
            for item in os.listdir(platforms_path):
                if item.startswith("android-") and os.path.isdir(os.path.join(platforms_path, item)):
                    api_level = item.replace("android-", "")
                    try:
                        self.environment_info["available_apis"].append(int(api_level))
                    except ValueError:
                        pass
            self.environment_info["available_apis"].sort(reverse=True)
    
    def _detect_build_tools(self, sdk_path):
        """检测可用的 Build Tools 版本"""
        build_tools_path = os.path.join(sdk_path, "build-tools")
        if os.path.exists(build_tools_path):
            for item in os.listdir(build_tools_path):
                if os.path.isdir(os.path.join(build_tools_path, item)):
                    self.environment_info["build_tools"].append(item)
            self.environment_info["build_tools"].sort(reverse=True)
    
    def detect_jdk(self):
        """检测 JDK 版本"""
        try:
            result = subprocess.run(["java", "-version"], capture_output=True, text=True, stderr=subprocess.STDOUT)
            if result.returncode == 0:
                output = result.stderr if result.stderr else result.stdout
                # 解析 Java 版本
                for line in output.split('\n'):
                    if 'version' in line:
                        # 提取版本号
                        import re
                        version_match = re.search(r'"([^"]*)"', line)
                        if version_match:
                            version = version_match.group(1)
                            self.environment_info["jdk_version"] = version
                            break
        except FileNotFoundError:
            self.environment_info["jdk_version"] = "Not found"
    
    def detect_gradle(self):
        """检测 Gradle 版本"""
        try:
            result = subprocess.run(["gradle", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if line.startswith("Gradle"):
                        self.environment_info["gradle_version"] = line.split()[1]
                        break
        except FileNotFoundError:
            self.environment_info["gradle_version"] = "Not found"
    
    def detect_android_studio(self):
        """检测 Android Studio"""
        possible_paths = []
        
        if self.system == "Windows":
            possible_paths = [
                "C:\\Program Files\\Android\\Android Studio",
                "C:\\Program Files (x86)\\Android\\Android Studio",
                os.path.join(os.path.expanduser("~"), "AppData", "Local", "Android", "Android Studio")
            ]
        elif self.system == "Darwin":
            possible_paths = [
                "/Applications/Android Studio.app"
            ]
        else:
            possible_paths = [
                "/opt/android-studio",
                os.path.join(os.path.expanduser("~"), "android-studio")
            ]
        
        for path in possible_paths:
            if os.path.exists(path):
                self.environment_info["android_studio"] = path
                break
    
    def generate_recommendations(self):
        """根据检测结果生成推荐配置"""
        recommendations = {}
        
        # 推荐 compileSdk
        if self.environment_info["available_apis"]:
            highest_api = max(self.environment_info["available_apis"])
            # 选择一个稳定的 API 级别
            if highest_api >= 34:
                recommendations["compileSdk"] = 34
                recommendations["targetSdk"] = 34
            elif highest_api >= 33:
                recommendations["compileSdk"] = 33
                recommendations["targetSdk"] = 33
            elif highest_api >= 32:
                recommendations["compileSdk"] = 32
                recommendations["targetSdk"] = 32
            elif highest_api >= 31:
                recommendations["compileSdk"] = 31
                recommendations["targetSdk"] = 31
            else:
                recommendations["compileSdk"] = highest_api
                recommendations["targetSdk"] = highest_api
        else:
            recommendations["compileSdk"] = 30
            recommendations["targetSdk"] = 30
        
        # 推荐依赖版本
        compile_sdk = recommendations.get("compileSdk", 30)
        if compile_sdk >= 34:
            recommendations["dependencies"] = {
                "core_ktx": "1.12.0",
                "appcompat": "1.6.1",
                "material": "1.11.0",
                "lifecycle": "2.7.0",
                "room": "2.6.1"
            }
        elif compile_sdk >= 33:
            recommendations["dependencies"] = {
                "core_ktx": "1.10.1",
                "appcompat": "1.6.1",
                "material": "1.9.0",
                "lifecycle": "2.6.2",
                "room": "2.5.2"
            }
        elif compile_sdk >= 31:
            recommendations["dependencies"] = {
                "core_ktx": "1.8.0",
                "appcompat": "1.5.0",
                "material": "1.6.1",
                "lifecycle": "2.5.1",
                "room": "2.4.3"
            }
        else:
            recommendations["dependencies"] = {
                "core_ktx": "1.7.0",
                "appcompat": "1.4.2",
                "material": "1.6.1",
                "lifecycle": "2.4.1",
                "room": "2.4.2"
            }
        
        self.environment_info["recommended_config"] = recommendations
    
    def run_detection(self):
        """运行完整的环境检测"""
        print("🔍 正在检测 Android 开发环境...")
        
        self.detect_android_sdk()
        self.detect_jdk()
        self.detect_gradle()
        self.detect_android_studio()
        self.generate_recommendations()
        
        return self.environment_info
    
    def print_report(self):
        """打印检测报告"""
        info = self.environment_info
        
        print("\n" + "="*60)
        print("📱 Android 开发环境检测报告")
        print("="*60)
        
        print(f"\n🖥️  操作系统: {info['system']}")
        
        print(f"\n📁 Android SDK: {info['android_sdk'] or '❌ 未找到'}")
        if info['available_apis']:
            print(f"   可用 API 级别: {', '.join(map(str, info['available_apis']))}")
            print(f"   最高 API 级别: {max(info['available_apis'])}")
        
        if info['build_tools']:
            print(f"   Build Tools: {', '.join(info['build_tools'][:3])}...")
        
        print(f"\n☕ JDK 版本: {info['jdk_version'] or '❌ 未找到'}")
        print(f"\n🔧 Gradle 版本: {info['gradle_version'] or '❌ 未找到'}")
        print(f"\n🎨 Android Studio: {info['android_studio'] or '❌ 未找到'}")
        
        print(f"\n✅ 推荐配置:")
        rec = info['recommended_config']
        if rec:
            print(f"   compileSdk: {rec.get('compileSdk', 'N/A')}")
            print(f"   targetSdk: {rec.get('targetSdk', 'N/A')}")
            if 'dependencies' in rec:
                print(f"   核心依赖版本:")
                deps = rec['dependencies']
                for key, version in deps.items():
                    print(f"     {key}: {version}")
        
        print("\n" + "="*60)
    
    def save_config(self, filename="environment_config.json"):
        """保存配置到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.environment_info, f, indent=2, ensure_ascii=False)
        print(f"\n💾 环境配置已保存到: {filename}")

def main():
    detector = AndroidEnvironmentDetector()
    
    try:
        detector.run_detection()
        detector.print_report()
        detector.save_config()
        
        # 生成修复建议
        info = detector.environment_info
        if not info['android_sdk']:
            print("\n⚠️  警告: 未找到 Android SDK，请安装 Android Studio")
        elif not info['available_apis']:
            print("\n⚠️  警告: 未找到可用的 Android API，请在 SDK Manager 中安装")
        else:
            print(f"\n🎯 建议: 使用 API {info['recommended_config'].get('compileSdk', 30)} 进行开发")
        
        return info
        
    except Exception as e:
        print(f"\n❌ 检测过程中出现错误: {e}")
        return None

if __name__ == "__main__":
    main()
