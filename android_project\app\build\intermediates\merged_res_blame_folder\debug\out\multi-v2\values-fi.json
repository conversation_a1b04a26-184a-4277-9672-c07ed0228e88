{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-38:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\031e7bcb191513255b93c6f8738cef28\\transformed\\core-1.10.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3392,3488,3590,3688,3793,3898,4010,8680", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3483,3585,3683,3788,3893,4005,4121,8776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2303021cb7f3f78c4bc1b8231e5ae854\\transformed\\appcompat-1.6.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,8599", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,8675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\151ecdfd05d4da12e68bb0fff234359b\\transformed\\material-1.9.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1025,1118,1193,1258,1346,1411,1477,1535,1606,1672,1726,1836,1896,1960,2014,2087,2203,2287,2368,2501,2586,2671,2761,2813,2864,2930,3007,3089,3173,3247,3326,3403,3475,3564,3640,3731,3826,3900,3973,4067,4121,4193,4279,4365,4427,4491,4554,4655,4757,4852,4955,5011,5066", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,89,51,50,65,76,81,83,73,78,76,71,88,75,90,94,73,72,93,53,71,85,85,61,63,62,100,101,94,102,55,54,78", "endOffsets": "263,339,413,496,585,667,763,871,955,1020,1113,1188,1253,1341,1406,1472,1530,1601,1667,1721,1831,1891,1955,2009,2082,2198,2282,2363,2496,2581,2666,2756,2808,2859,2925,3002,3084,3168,3242,3321,3398,3470,3559,3635,3726,3821,3895,3968,4062,4116,4188,4274,4360,4422,4486,4549,4650,4752,4847,4950,5006,5061,5140"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2988,3064,3138,3221,3310,4126,4222,4330,4414,4479,4572,4647,4712,4800,4865,4931,4989,5060,5126,5180,5290,5350,5414,5468,5541,5657,5741,5822,5955,6040,6125,6215,6267,6318,6384,6461,6543,6627,6701,6780,6857,6929,7018,7094,7185,7280,7354,7427,7521,7575,7647,7733,7819,7881,7945,8008,8109,8211,8306,8409,8465,8520", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,89,51,50,65,76,81,83,73,78,76,71,88,75,90,94,73,72,93,53,71,85,85,61,63,62,100,101,94,102,55,54,78", "endOffsets": "313,3059,3133,3216,3305,3387,4217,4325,4409,4474,4567,4642,4707,4795,4860,4926,4984,5055,5121,5175,5285,5345,5409,5463,5536,5652,5736,5817,5950,6035,6120,6210,6262,6313,6379,6456,6538,6622,6696,6775,6852,6924,7013,7089,7180,7275,7349,7422,7516,7570,7642,7728,7814,7876,7940,8003,8104,8206,8301,8404,8460,8515,8594"}}]}]}