1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.quantumhealing.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
12-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:6:5-77
12-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:7:5-68
13-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:7:22-65
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:8:5-66
14-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
15-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:9:5-77
15-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:9:22-74
16
17    <permission
17-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
18        android:name="com.quantumhealing.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.quantumhealing.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
22
23    <application
23-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:11:5-40:19
24        android:allowBackup="true"
24-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:12:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:13:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="true"
29        android:fullBackupContent="@xml/backup_rules"
29-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:14:9-54
30        android:icon="@mipmap/ic_launcher"
30-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:15:9-43
31        android:label="@string/app_name"
31-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:16:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:17:9-54
33        android:supportsRtl="true"
33-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:18:9-35
34        android:testOnly="true"
35        android:theme="@style/Theme.QuantumHealing" >
35-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:19:9-52
36
37        <!-- 主Activity -->
38        <activity
38-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:23:9-31:20
39            android:name="com.quantumhealing.app.MainActivity"
39-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:24:13-41
40            android:exported="true"
40-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:25:13-36
41            android:theme="@style/Theme.QuantumHealing" >
41-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:26:13-56
42            <intent-filter>
42-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:27:13-30:29
43                <action android:name="android.intent.action.MAIN" />
43-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:28:17-69
43-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:28:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:29:17-77
45-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:29:27-74
46            </intent-filter>
47        </activity>
48
49        <!-- 量子疗愈后台服务 -->
50        <service
50-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:34:9-38:58
51            android:name="com.quantumhealing.app.service.QuantumHealingService"
51-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:35:13-58
52            android:enabled="true"
52-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:36:13-35
53            android:exported="false"
53-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:37:13-37
54            android:foregroundServiceType="specialUse" />
54-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:38:13-55
55
56        <provider
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c759b0ea295dd194e15fa57684e3763e\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
57            android:name="androidx.startup.InitializationProvider"
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c759b0ea295dd194e15fa57684e3763e\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
58            android:authorities="com.quantumhealing.app.androidx-startup"
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c759b0ea295dd194e15fa57684e3763e\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
59            android:exported="false" >
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c759b0ea295dd194e15fa57684e3763e\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
60            <meta-data
60-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c759b0ea295dd194e15fa57684e3763e\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.emoji2.text.EmojiCompatInitializer"
61-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c759b0ea295dd194e15fa57684e3763e\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
62                android:value="androidx.startup" />
62-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c759b0ea295dd194e15fa57684e3763e\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\087a0a10946b3e96116f8387900aa656\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
64-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\087a0a10946b3e96116f8387900aa656\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
65                android:value="androidx.startup" />
65-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\087a0a10946b3e96116f8387900aa656\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
66            <meta-data
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
68                android:value="androidx.startup" />
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
69        </provider>
70
71        <service
71-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\64d850e58893a5197f864a6b0025f5ac\transformed\room-runtime-2.5.2\AndroidManifest.xml:24:9-28:63
72            android:name="androidx.room.MultiInstanceInvalidationService"
72-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\64d850e58893a5197f864a6b0025f5ac\transformed\room-runtime-2.5.2\AndroidManifest.xml:25:13-74
73            android:directBootAware="true"
73-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\64d850e58893a5197f864a6b0025f5ac\transformed\room-runtime-2.5.2\AndroidManifest.xml:26:13-43
74            android:exported="false" />
74-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\64d850e58893a5197f864a6b0025f5ac\transformed\room-runtime-2.5.2\AndroidManifest.xml:27:13-37
75
76        <receiver
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
77            android:name="androidx.profileinstaller.ProfileInstallReceiver"
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
78            android:directBootAware="false"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
79            android:enabled="true"
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
80            android:exported="true"
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
81            android:permission="android.permission.DUMP" >
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
83                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
86                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
89                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
92                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f306f654ba8ff11bb6c9f652e49ead8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
93            </intent-filter>
94        </receiver>
95    </application>
96
97</manifest>
