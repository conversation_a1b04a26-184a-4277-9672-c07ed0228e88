{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-38:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2303021cb7f3f78c4bc1b8231e5ae854\\transformed\\appcompat-1.6.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,2869", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,2954"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,441,551,658,744,848,968,1047,1128,1219,1312,1415,1510,1610,1703,1798,1894,1985,2075,2164,2274,2378,2484,2595,2699,2817,2980,8612", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "436,546,653,739,843,963,1042,1123,1214,1307,1410,1505,1605,1698,1793,1889,1980,2070,2159,2269,2373,2479,2590,2694,2812,2975,3081,8697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\031e7bcb191513255b93c6f8738cef28\\transformed\\core-1.10.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3493,3596,3698,3801,3906,4007,4109,8702", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3591,3693,3796,3901,4002,4104,4223,8798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\151ecdfd05d4da12e68bb0fff234359b\\transformed\\material-1.9.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,773,878,957,1022,1111,1176,1235,1321,1385,1449,1512,1585,1649,1703,1815,1873,1935,1989,2061,2183,2270,2356,2496,2573,2654,2745,2799,2850,2916,2986,3063,3150,3221,3298,3367,3436,3527,3599,3688,3777,3851,3923,4009,4059,4125,4205,4289,4351,4415,4478,4578,4675,4767,4866,4924,4979", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,81,77,76,85,83,93,104,78,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,85,139,76,80,90,53,50,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,57,54,83", "endOffsets": "267,349,427,504,590,674,768,873,952,1017,1106,1171,1230,1316,1380,1444,1507,1580,1644,1698,1810,1868,1930,1984,2056,2178,2265,2351,2491,2568,2649,2740,2794,2845,2911,2981,3058,3145,3216,3293,3362,3431,3522,3594,3683,3772,3846,3918,4004,4054,4120,4200,4284,4346,4410,4473,4573,4670,4762,4861,4919,4974,5058"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3086,3168,3246,3323,3409,4228,4322,4427,4506,4571,4660,4725,4784,4870,4934,4998,5061,5134,5198,5252,5364,5422,5484,5538,5610,5732,5819,5905,6045,6122,6203,6294,6348,6399,6465,6535,6612,6699,6770,6847,6916,6985,7076,7148,7237,7326,7400,7472,7558,7608,7674,7754,7838,7900,7964,8027,8127,8224,8316,8415,8473,8528", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,81,77,76,85,83,93,104,78,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,85,139,76,80,90,53,50,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,57,54,83", "endOffsets": "317,3163,3241,3318,3404,3488,4317,4422,4501,4566,4655,4720,4779,4865,4929,4993,5056,5129,5193,5247,5359,5417,5479,5533,5605,5727,5814,5900,6040,6117,6198,6289,6343,6394,6460,6530,6607,6694,6765,6842,6911,6980,7071,7143,7232,7321,7395,7467,7553,7603,7669,7749,7833,7895,7959,8022,8122,8219,8311,8410,8468,8523,8607"}}]}]}