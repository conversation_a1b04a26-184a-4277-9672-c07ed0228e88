{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-33:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,400,492,618,699,764,863,939,1000,1089,1156,1210,1278,1338,1392,1509,1569,1631,1685,1757,1879,1963,2055,2162,2240,2322,2410,2477,2543,2615,2692,2776,2848,2925,2999,3070,3158,3229,3322,3417,3491,3565,3661,3713,3780,3866,3954,4016,4080,4143,4253,4349,4448,4546", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,76,91,125,80,64,98,75,60,88,66,53,67,59,53,116,59,61,53,71,121,83,91,106,77,81,87,66,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,78", "endOffsets": "318,395,487,613,694,759,858,934,995,1084,1151,1205,1273,1333,1387,1504,1564,1626,1680,1752,1874,1958,2050,2157,2235,2317,2405,2472,2538,2610,2687,2771,2843,2920,2994,3065,3153,3224,3317,3412,3486,3560,3656,3708,3775,3861,3949,4011,4075,4138,4248,4344,4443,4541,4620"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3067,3144,3236,3362,3443,3508,3607,3683,3744,3833,3900,3954,4022,4082,4136,4253,4313,4375,4429,4501,4623,4707,4799,4906,4984,5066,5154,5221,5287,5359,5436,5520,5592,5669,5743,5814,5902,5973,6066,6161,6235,6309,6405,6457,6524,6610,6698,6760,6824,6887,6997,7093,7192,7290", "endLines": "7,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "endColumns": "12,76,91,125,80,64,98,75,60,88,66,53,67,59,53,116,59,61,53,71,121,83,91,106,77,81,87,66,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,78", "endOffsets": "368,3139,3231,3357,3438,3503,3602,3678,3739,3828,3895,3949,4017,4077,4131,4248,4308,4370,4424,4496,4618,4702,4794,4901,4979,5061,5149,5216,5282,5354,5431,5515,5587,5664,5738,5809,5897,5968,6061,6156,6230,6304,6400,6452,6519,6605,6693,6755,6819,6882,6992,7088,7187,7285,7364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d780b2c0a0ca762ae8fc4516db86618\\transformed\\appcompat-1.4.2\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "373,480,582,692,778,883,1000,1078,1154,1245,1338,1433,1527,1621,1714,1809,1906,1997,2088,2172,2276,2388,2487,2593,2704,2806,2969,7369", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "475,577,687,773,878,995,1073,1149,1240,1333,1428,1522,1616,1709,1804,1901,1992,2083,2167,2271,2383,2482,2588,2699,2801,2964,3062,7447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c33bda6cfdbe9dfccb7561f3cbbee362\\transformed\\core-1.7.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "90", "startColumns": "4", "startOffsets": "7452", "endColumns": "100", "endOffsets": "7548"}}]}]}