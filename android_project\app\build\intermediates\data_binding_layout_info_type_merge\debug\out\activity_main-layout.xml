<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.quantumhealing.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_main_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="323" endOffset="14"/></Target><Target id="@+id/etName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="49" startOffset="24" endLine="53" endOffset="64"/></Target><Target id="@+id/actvGender" view="AutoCompleteTextView"><Expressions/><location startLine="64" startOffset="24" endLine="68" endOffset="54"/></Target><Target id="@+id/actvYear" view="AutoCompleteTextView"><Expressions/><location startLine="93" startOffset="28" endLine="97" endOffset="58"/></Target><Target id="@+id/actvMonth" view="AutoCompleteTextView"><Expressions/><location startLine="109" startOffset="28" endLine="113" endOffset="58"/></Target><Target id="@+id/actvDay" view="AutoCompleteTextView"><Expressions/><location startLine="124" startOffset="28" endLine="128" endOffset="58"/></Target><Target id="@+id/actvHour" view="AutoCompleteTextView"><Expressions/><location startLine="154" startOffset="28" endLine="158" endOffset="58"/></Target><Target id="@+id/actvMinute" view="AutoCompleteTextView"><Expressions/><location startLine="169" startOffset="28" endLine="173" endOffset="58"/></Target><Target id="@+id/actvYearPillar" view="AutoCompleteTextView"><Expressions/><location startLine="185" startOffset="24" endLine="189" endOffset="54"/></Target><Target id="@+id/actvHourPillar" view="AutoCompleteTextView"><Expressions/><location startLine="200" startOffset="24" endLine="204" endOffset="54"/></Target><Target id="@+id/actvLocation" view="AutoCompleteTextView"><Expressions/><location startLine="215" startOffset="24" endLine="219" endOffset="54"/></Target><Target id="@+id/actvDisease" view="AutoCompleteTextView"><Expressions/><location startLine="230" startOffset="24" endLine="234" endOffset="54"/></Target><Target id="@+id/btnAdd" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="244" startOffset="24" endLine="249" endOffset="49"/></Target><Target id="@+id/btnUpdate" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="251" startOffset="24" endLine="258" endOffset="92"/></Target><Target id="@+id/btnDelete" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="260" startOffset="24" endLine="267" endOffset="92"/></Target><Target id="@+id/btnStartSystem" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="269" startOffset="24" endLine="274" endOffset="94"/></Target><Target id="@+id/rvPersons" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="304" startOffset="12" endLine="308" endOffset="43"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="311" startOffset="12" endLine="319" endOffset="39"/></Target></Targets></Layout>