package com.quantumhealing.app.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.text.SimpleDateFormat
import java.util.*

/**
 * 疗愈事件数据模型
 * 对应原Python程序中的event字典
 */
@Entity(tableName = "healing_events")
data class HealingEvent(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    val personId: Long,                  // 关联的人员ID
    val personName: String,              // 人员姓名
    val timestamp: Long,                 // 时间戳
    val phase: String,                   // 量子相位
    val wave: String,                    // 量子波
    val energyLevel: Int,                // 能量等级 (80-100)
    val frequency: String                // 疗愈频率
) {
    /**
     * 获取格式化的时间戳
     */
    fun getFormattedTimestamp(): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }
    
    /**
     * 获取显示用的事件信息
     */
    fun getDisplayInfo(): String {
        return "${getFormattedTimestamp()} | $personName | $frequency | $wave | $phase | $energyLevel%"
    }
    
    companion object {
        /**
         * 创建新的疗愈事件
         * 对应原程序中的事件创建逻辑
         */
        fun create(
            personId: Long,
            personName: String,
            frequency: String
        ): HealingEvent {
            val now = System.currentTimeMillis()
            val phase = generateQuantumPhase(now)
            val wave = generateHealingWave(frequency)
            val energyLevel = (80..100).random()
            
            return HealingEvent(
                personId = personId,
                personName = personName,
                timestamp = now,
                phase = phase,
                wave = wave,
                energyLevel = energyLevel,
                frequency = frequency
            )
        }
        
        /**
         * 生成量子相位
         * 对应原程序的quantum_handshake方法
         */
        private fun generateQuantumPhase(timestamp: Long): String {
            val microsecond = (timestamp % 1000) * 1000
            val second = (timestamp / 1000) % 60
            val phaseCode = "${microsecond % 314}π/${second}λ"
            return phaseCode
        }
        
        /**
         * 生成疗愈波
         * 对应原程序的send_healing_wave方法
         */
        private fun generateHealingWave(frequency: String): String {
            val freqValue = frequency.split("Hz")[0]
            val randomCode = (1000..9999).random()
            return "M42_${freqValue}Se_$randomCode"
        }
    }
}
