package com.quantumhealing.app.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.quantumhealing.app.data.model.Person

/**
 * 人员数据访问对象
 */
@Dao
interface PersonDao {
    
    @Query("SELECT * FROM persons ORDER BY name ASC")
    fun getAllPersons(): LiveData<List<Person>>
    
    @Query("SELECT * FROM persons WHERE id = :id")
    suspend fun getPersonById(id: Long): Person?
    
    @Query("SELECT * FROM persons WHERE name = :name")
    suspend fun getPersonByName(name: String): Person?
    
    @Query("SELECT * FROM persons WHERE isActive = 1")
    suspend fun getActivePersons(): List<Person>
    
    @Insert
    suspend fun insertPerson(person: Person): Long
    
    @Update
    suspend fun updatePerson(person: Person)
    
    @Delete
    suspend fun deletePerson(person: Person)
    
    @Query("DELETE FROM persons WHERE id = :id")
    suspend fun deletePersonById(id: Long)
    
    @Query("UPDATE persons SET isActive = :isActive WHERE id = :id")
    suspend fun updatePersonActiveStatus(id: Long, isActive: Boolean)
    
    @Query("UPDATE persons SET isActive = 0")
    suspend fun deactivateAllPersons()
    
    @Query("SELECT COUNT(*) FROM persons")
    suspend fun getPersonCount(): Int
    
    @Query("SELECT COUNT(*) FROM persons WHERE isActive = 1")
    suspend fun getActivePersonCount(): Int
}
