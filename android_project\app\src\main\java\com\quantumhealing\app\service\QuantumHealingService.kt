package com.quantumhealing.app.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.quantumhealing.app.R
import com.quantumhealing.app.data.Constants
import com.quantumhealing.app.data.database.QuantumHealingDatabase
import com.quantumhealing.app.data.model.HealingEvent
import com.quantumhealing.app.data.model.Person
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 量子疗愈后台服务
 * 对应原Python程序的QuantumHealer和HealingOrchestrator类
 */
class QuantumHealingService : LifecycleService() {
    
    companion object {
        private const val TAG = "QuantumHealingService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "quantum_healing_channel"
        private const val HEALING_INTERVAL = 127L // 1/7.83Hz ≈ 127ms
    }
    
    private val binder = LocalBinder()
    private val healingJobs = ConcurrentHashMap<Long, Job>()
    private var isSystemRunning = false
    private var quantumFieldLevel = 100
    
    private lateinit var database: QuantumHealingDatabase
    private lateinit var notificationManager: NotificationManager
    
    // 服务状态监听器
    private var statusListener: ((String) -> Unit)? = null
    private var eventListener: ((HealingEvent) -> Unit)? = null
    
    inner class LocalBinder : Binder() {
        fun getService(): QuantumHealingService = this@QuantumHealingService
    }
    
    override fun onCreate() {
        super.onCreate()
        database = QuantumHealingDatabase.getDatabase(this)
        notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        createNotificationChannel()
        Log.d(TAG, "量子疗愈服务已创建")
    }
    
    override fun onBind(intent: Intent): IBinder {
        super.onBind(intent)
        return binder
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        
        when (intent?.action) {
            "START_HEALING" -> startHealingSystem()
            "STOP_HEALING" -> stopHealingSystem()
        }
        
        return START_STICKY
    }
    
    /**
     * 启动疗愈系统
     */
    fun startHealingSystem() {
        if (isSystemRunning) {
            Log.w(TAG, "疗愈系统已在运行中")
            return
        }
        
        lifecycleScope.launch {
            try {
                val activePersons = database.personDao().getActivePersons()
                if (activePersons.isEmpty()) {
                    statusListener?.invoke("没有激活的人员，无法启动系统")
                    return@launch
                }
                
                isSystemRunning = true
                startForeground(NOTIFICATION_ID, createNotification("量子疗愈系统运行中"))
                
                // 为每个激活的人员启动疗愈线程
                activePersons.forEach { person ->
                    startHealingForPerson(person)
                }
                
                // 启动监控线程
                startMonitoringThread()
                
                statusListener?.invoke("量子疗愈系统已启动 (${activePersons.size} 人在场)")
                Log.i(TAG, "量子疗愈系统已启动，共 ${activePersons.size} 人")
                
            } catch (e: Exception) {
                Log.e(TAG, "启动疗愈系统失败", e)
                statusListener?.invoke("启动失败: ${e.message}")
            }
        }
    }
    
    /**
     * 停止疗愈系统
     */
    fun stopHealingSystem() {
        if (!isSystemRunning) {
            Log.w(TAG, "疗愈系统未运行")
            return
        }
        
        isSystemRunning = false
        
        // 停止所有疗愈线程
        healingJobs.values.forEach { job ->
            job.cancel()
        }
        healingJobs.clear()
        
        // 停止前台服务
        stopForeground(true)
        
        statusListener?.invoke("量子疗愈系统已停止")
        Log.i(TAG, "量子疗愈系统已停止")
    }
    
    /**
     * 为指定人员启动疗愈线程
     * 对应原Python程序的QuantumHealer.run方法
     */
    private fun startHealingForPerson(person: Person) {
        val job = lifecycleScope.launch {
            Log.d(TAG, "✦ 启动量子纠缠通道 [${person.name}] 生物签名: ${person.generateBioSignature()}")
            
            while (isActive && isSystemRunning) {
                try {
                    // 选择疗愈频率
                    val frequencies = Constants.HEALING_FREQUENCIES[person.disease] 
                        ?: Constants.HEALING_FREQUENCIES["通用"]!!
                    val frequency = frequencies.random()
                    
                    // 创建疗愈事件
                    val event = HealingEvent.create(person.id, person.name, frequency)
                    
                    // 保存到数据库
                    database.healingEventDao().insertEvent(event)
                    
                    // 通知监听器
                    eventListener?.invoke(event)
                    
                    Log.v(TAG, "疗愈事件: ${event.getDisplayInfo()}")
                    
                    // 量子叠加态休眠 (7.83Hz基础共振)
                    delay(HEALING_INTERVAL)
                    
                } catch (e: Exception) {
                    Log.e(TAG, "量子场扰动: ${e.message}")
                    delay(5000) // 错误时延长休眠
                }
            }
            
            Log.d(TAG, "♡ 关闭量子通道 [${person.name}]")
        }
        
        healingJobs[person.id] = job
    }
    
    /**
     * 启动监控线程
     * 对应原Python程序的HealingOrchestrator.monitor方法
     */
    private fun startMonitoringThread() {
        lifecycleScope.launch {
            while (isSystemRunning) {
                try {
                    delay(60000) // 每分钟监控一次
                    
                    val activeCount = database.personDao().getActivePersonCount()
                    val eventCount = database.healingEventDao().getEventCount()
                    
                    // 自动场强调节
                    quantumFieldLevel = when {
                        activeCount > 5 -> minOf(120, quantumFieldLevel + 2)
                        else -> maxOf(80, quantumFieldLevel - 1)
                    }
                    
                    val status = "量子场状态 | 活跃连接: $activeCount | 场强: $quantumFieldLevel% | 总事件: $eventCount"
                    statusListener?.invoke(status)
                    
                    // 更新通知
                    val notification = createNotification("量子疗愈运行中 ($activeCount 人)")
                    notificationManager.notify(NOTIFICATION_ID, notification)
                    
                    Log.d(TAG, status)
                    
                } catch (e: Exception) {
                    Log.e(TAG, "监控线程错误", e)
                }
            }
        }
    }
    
    /**
     * 添加人员到疗愈系统
     */
    fun addPersonToHealing(person: Person) {
        if (!isSystemRunning) return
        
        lifecycleScope.launch {
            database.personDao().updatePersonActiveStatus(person.id, true)
            startHealingForPerson(person.copy(isActive = true))
            statusListener?.invoke("★ 量子场加入: ${person.name} (${person.disease})")
        }
    }
    
    /**
     * 从疗愈系统移除人员
     */
    fun removePersonFromHealing(personId: Long, personName: String) {
        healingJobs[personId]?.cancel()
        healingJobs.remove(personId)
        
        lifecycleScope.launch {
            database.personDao().updatePersonActiveStatus(personId, false)
            statusListener?.invoke("♻️ 量子场移除: $personName")
        }
    }
    
    /**
     * 设置状态监听器
     */
    fun setStatusListener(listener: (String) -> Unit) {
        statusListener = listener
    }
    
    /**
     * 设置事件监听器
     */
    fun setEventListener(listener: (HealingEvent) -> Unit) {
        eventListener = listener
    }
    
    /**
     * 获取系统运行状态
     */
    fun isRunning(): Boolean = isSystemRunning
    
    /**
     * 获取量子场强度
     */
    fun getQuantumFieldLevel(): Int = quantumFieldLevel
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            "量子疗愈服务",
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "量子疗愈系统后台服务通知"
            setShowBadge(false)
        }
        notificationManager.createNotificationChannel(channel)
    }
    
    /**
     * 创建通知
     */
    private fun createNotification(content: String): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("量子加持能量疗愈系统")
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_notification)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopHealingSystem()
        Log.d(TAG, "量子疗愈服务已销毁")
    }
}
