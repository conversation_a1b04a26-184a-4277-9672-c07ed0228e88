import tkinter as tk
from tkinter import ttk, messagebox
import threading 
import time 
import datetime 
import random 
import math 
import sys 



 

# 量子共振频率库 (基于Schumann共振与生物节律)
HEALING_FREQUENCIES = {
     # 通用
    "通用": ["7.83Hz", "14.3Hz", "20.14Hz"],

    # 重大系统性疾病
    "癌症（恶性肿瘤）": ["7.83Hz", "152Hz", "20.14Hz"],
    "心脏病": ["7.83Hz", "20.14Hz", "33.33Hz"],
    "艾滋病": ["7.83Hz", "40.68Hz", "33.33Hz"],
    "系统性红斑狼疮": ["7.83Hz", "14.3Hz", "40.68Hz"],
    "系统性硬皮病（硬皮病）": ["7.83Hz", "20.14Hz", "14.3Hz"],
    
    # 神经系统疾病 
    "癫痫": ["7.83Hz", "33.33Hz", "14.3Hz"],
    "帕金森病": ["7.83Hz", "11.79Hz", "40.68Hz"], 
    "重症肌无力": ["7.83Hz", "20.14Hz", "33.33Hz"],
    "多发性硬化": ["7.83Hz", "14.3Hz", "26.4Hz"],
    
    # 代谢性疾病 
    "糖尿病": ["7.83Hz", "40.68Hz", "152Hz"], 
    "痛风": ["7.83Hz", "33.33Hz", "14.3Hz"],
    "甲状腺功能减退症": ["7.83Hz", "20.14Hz", "40.68Hz"],
    "甲亢（甲亢性心脏病）": ["7.83Hz", "14.3Hz", "33.33Hz"],
    
    # 免疫性疾病 
    "类风湿性关节炎": ["7.83Hz", "40.68Hz", "14.3Hz"],
    "强直性脊柱炎": ["7.83Hz", "26.4Hz", "20.14Hz"],
    "自身免疫性肝炎": ["7.83Hz", "33.33Hz", "14.3Hz"],
    
    # 心脑血管疾病 
    "冠心病": ["7.83Hz", "20.14Hz", "14.3Hz"],
    "脑梗塞": ["7.83Hz", "33.33Hz", "40.68Hz"],
    "高血压": ["7.83Hz", "14.3Hz", "20.14Hz"],
    
    # 呼吸系统疾病  
    "哮喘": ["7.83Hz", "40.68Hz", "33.33Hz"],
    "矽肺": ["7.83Hz", "20.14Hz", "152Hz"],
    "肺纤维化": ["7.83Hz", "14.3Hz", "40.68Hz"],
    
    # 消化系统疾病 
    "肝硬化": ["7.83Hz", "33.33Hz", "14.3Hz"],
    "溃疡性结肠炎": ["7.83Hz", "40.68Hz", "20.14Hz"],
    "慢性胰腺炎": ["7.83Hz", "14.3Hz", "33.33Hz"],
    
    # 血液疾病 
    "白血病": ["7.83Hz", "152Hz", "40.68Hz"],
    "再生障碍性贫血": ["7.83Hz", "20.14Hz", "33.33Hz"],
    "血小板减少性紫癜": ["7.83Hz", "14.3Hz", "40.68Hz"],
    
    # 特殊病症 
    "烧伤烫伤": ["7.83Hz", "40.68Hz", "152Hz"],
    "先天性疾病": ["7.83Hz", "33.33Hz", "20.14Hz"],
    "精神分裂症": ["7.83Hz", "14.3Hz", "26.4Hz"]
}





LOCATION_MAPPING = {

    # 直辖市
    "北京": "(116.40, 39.90)",
    "上海": "(121.47, 31.23)",
    "天津市": "(117.2, 39.12)",
    "重庆市": "(106.55, 29.56)",


    # 安徽省 
    "安徽合肥": "(117.23, 31.82)",
    "安徽宿州": "(116.98, 33.63)",
    "安徽淮北": "(116.80, 33.95)",
    "安徽阜阳": "(115.82, 32.90)",
    "安徽蚌埠": "(117.38, 32.92)",
    "安徽淮南": "(117.00, 32.63)",
    "安徽滁州": "(118.32, 32.30)",
    "安徽马鞍山": "(118.50, 31.70)",
    "安徽芜湖": "(118.38, 31.33)",
    "安徽铜陵": "(117.82, 30.93)",
    "安徽安庆": "(117.05, 30.53)",
    "安徽黄山": "(118.33, 29.72)",
    "安徽六安": "(116.50, 31.77)",
    "安徽池州": "(117.48, 30.67)",
    "安徽宣城": "(118.75, 30.95)",
    "安徽亳州": "(115.78, 33.85)",
    "安徽界首": "(115.37, 33.25)",
    "安徽明光": "(117.98, 32.78)",
    "安徽天长": "(119.00, 32.68)",
    "安徽桐城": "(116.95, 31.05)",
    "安徽宁国": "(118.98, 30.63)",
    "安徽巢湖": "(117.87, 31.60)",  # 2011年行政区划调整前数据 
 
    # 福建省 
    "福建福州": "(119.30, 26.08)",
    "福建厦门": "(118.08, 24.48)",
    "福建南平": "(118.17, 26.65)",
    "福建三明": "(117.62, 26.27)",
    "福建莆田": "(119.00, 25.43)",
    "福建泉州": "(118.67, 24.88)",
    "福建漳州": "(117.65, 24.52)",
    "福建龙岩": "(117.03, 25.10)",
    "福建宁德": "(119.52, 26.67)",
    "福建福清": "(119.38, 25.72)",
    "福建邵武": "(117.48, 27.37)",
    "福建武夷山": "(118.03, 27.77)",
    "福建建瓯": "(118.32, 27.03)",
    "福建永安": "(117.37, 25.98)",
    "福建石狮": "(118.65, 24.73)",
    "福建晋江": "(118.58, 24.82)",
    "福建南安": "(118.38, 24.97)",
    "福建龙海": "(117.82, 24.45)",
    "福建漳平": "(117.42, 25.30)",
    "福建福安": "(119.65, 27.08)",
    "福建福鼎": "(120.22, 27.33)",
 
    # 甘肃省 
    "甘肃兰州": "(103.83, 36.05)",
    "甘肃天水": "(105.72, 34.58)",
    "甘肃嘉峪关": "(98.27, 39.80)",
    "甘肃金昌": "(102.18, 38.52)",
    "甘肃白银": "(104.18, 36.55)",
    "甘肃酒泉": "(98.52, 39.75)",
    "甘肃张掖": "(100.45, 38.93)",
    "甘肃武威": "(102.63, 37.93)",
    "甘肃庆阳": "(107.63, 35.73)",
    "甘肃平凉": "(106.67, 35.55)",
    "甘肃定西": "(104.62, 35.58)",
    "甘肃陇南": "(104.92, 33.40)",
    "甘肃玉门": "(97.05, 40.28)",
    "甘肃敦煌": "(94.67, 40.15)",
    "甘肃临夏": "(103.22, 35.60)",
    "甘肃合作": "(102.90, 35.00)",



    # 广东省
    "广东广州": "(113.26, 23.12)",
    "广东深圳": "(114.06, 22.55)",
    "广东清远": "(113.02, 23.68)",
    "广东韶关": "(113.60, 24.82)",
    "广东河源": "(114.70, 23.73)",
    "广东梅州": "(116.12, 24.28)",
    "广东潮州": "(116.62, 23.67)",
    "广东汕头": "(116.69, 23.35)",
    "广东揭阳": "(116.37, 23.55)",
    "广东汕尾": "(115.37, 22.78)",
    "广东惠州": "(114.42, 23.12)",
    "广东东莞": "(113.75, 23.05)",
    "广东珠海": "(113.57, 22.27)",
    "广东中山": "(113.38, 22.52)",
    "广东江门": "(113.08, 22.58)",
    "广东佛山": "(113.12, 23.02)",
    "广东肇庆": "(112.47, 23.05)",
    "广东云浮": "(112.03, 22.92)",
    "广东阳江": "(111.98, 21.87)",
    "广东茂名": "(110.88, 21.68)",
    "广东湛江": "(110.38, 21.20)",
    "广东英德": "(113.40, 24.18)",
    "广东连州": "(112.38, 24.78)",
    "广东乐昌": "(113.35, 25.13)",
    "广东南雄": "(114.30, 25.12)",
    "广东兴宁": "(115.73, 24.15)",
    "广东普宁": "(116.18, 23.30)",
    "广东陆丰": "(115.65, 22.95)",
    "广东恩平": "(112.30, 22.18)",
    "广东台山": "(112.78, 22.25)",
    "广东开平": "(112.67, 22.38)",
    "广东鹤山": "(112.97, 22.77)",
    "广东四会": "(112.73, 23.33)",
    "广东罗定": "(111.57, 22.77)",
    "广东阳春": "(111.78, 22.17)",
    "广东化州": "(110.63, 21.67)",
    "广东信宜": "(110.95, 22.35)",
    "广东高州": "(110.85, 21.92)",
    "广东吴川": "(110.78, 21.43)",
    "广东廉江": "(110.28, 21.62)",
    "广东雷州": "(110.08, 20.92)",
 
    # 贵州省
    "贵州贵阳": "(106.63, 26.65)",
    "贵州六盘水": "(104.83, 26.60)",
    "贵州遵义": "(106.92, 27.73)",
    "贵州安顺": "(105.95, 26.25)",
    "贵州毕节": "(105.30, 27.30)",
    "贵州铜仁": "(109.18, 27.72)",
    "贵州清镇": "(106.47, 26.55)",
    "贵州赤水": "(105.70, 28.58)",
    "贵州仁怀": "(106.42, 27.82)",
    "贵州凯里": "(107.97, 26.58)",
    "贵州都匀": "(107.52, 26.27)",
    "贵州兴义": "(104.90, 25.08)",
    "贵州福泉": "(107.50, 26.70)",
    "贵州盘州": "(104.47, 25.70)",


    # 河北省 
    "河北石家庄": "(114.51, 38.04)",
    "河北邯郸": "(114.49, 36.62)",
    "河北唐山": "(118.18, 39.63)",
    "河北保定": "(115.46, 38.87)",
    "河北秦皇岛": "(119.60, 39.94)",
    "河北邢台": "(114.50, 37.07)",
    "河北张家口": "(114.88, 40.82)",
    "河北承德": "(117.96, 40.95)",
    "河北沧州": "(116.84, 38.30)",
    "河北廊坊": "(116.70, 39.52)",
    "河北衡水": "(115.67, 37.73)",
    "河北定州": "(114.99, 38.52)",
    "河北辛集": "(115.22, 37.94)",
    "河北藁城": "(114.84, 38.03)",
    "河北晋州": "(115.04, 38.03)",
    "河北新乐": "(114.68, 38.34)",
    "河北鹿泉": "(114.31, 38.09)",
    "河北遵化": "(117.97, 40.19)",
    "河北迁安": "(118.70, 40.02)",
    "河北霸州": "(116.39, 39.12)",
    "河北三河": "(117.07, 39.98)",
    "河北涿州": "(115.97, 39.49)",
    "河北安国": "(115.33, 38.42)",
    "河北高碑店": "(115.87, 39.33)",
    "河北泊头": "(116.57, 38.08)",
    "河北任丘": "(116.10, 38.72)",
    "河北黄骅": "(117.35, 38.37)",
    "河北河间": "(116.09, 38.44)",
    "河北冀州": "(115.57, 37.55)",
    "河北深州": "(115.56, 38.02)",
    "河北南宫": "(115.41, 37.36)",
    "河北沙河": "(114.50, 36.86)",
    "河北武安": "(114.20, 36.70)",
 
    # 湖北省 
    "湖北武汉": "(114.31, 30.52)",
    "湖北十堰": "(110.79, 32.65)",
    "湖北襄阳": "(112.12, 32.02)",
    "湖北荆门": "(112.20, 31.03)",
    "湖北孝感": "(113.92, 30.93)",
    "湖北黄冈": "(114.87, 30.45)",
    "湖北鄂州": "(114.89, 30.40)",
    "湖北黄石": "(115.03, 30.20)",
    "湖北咸宁": "(114.33, 29.88)",
    "湖北荆州": "(112.24, 30.33)",
    "湖北宜昌": "(111.29, 30.70)",
    "湖北随州": "(113.37, 31.72)",
    "湖北仙桃": "(113.45, 30.36)",
    "湖北天门": "(113.17, 30.66)",
    "湖北潜江": "(112.90, 30.40)",
    "湖北丹江口": "(111.51, 32.54)",
    "湖北老河口": "(111.67, 32.38)",
    "湖北枣阳": "(112.75, 32.13)",
    "湖北宜城": "(112.26, 31.72)",
    "湖北钟祥": "(112.58, 31.17)",
    "湖北汉川": "(113.83, 30.66)",
    "湖北应城": "(113.57, 30.93)",
    "湖北安陆": "(113.69, 31.26)",
    "湖北广水": "(113.83, 31.62)",
    "湖北麻城": "(115.01, 31.18)",
    "湖北武穴": "(115.56, 29.85)",
    "湖北大冶": "(114.98, 30.10)",
    "湖北赤壁": "(113.88, 29.72)",
    "湖北石首": "(112.41, 29.72)",
    "湖北洪湖": "(113.48, 29.82)",
    "湖北松滋": "(111.78, 30.18)",
    "湖北宜都": "(111.45, 30.40)",
    "湖北枝江": "(111.76, 30.43)",
    "湖北当阳": "(111.79, 30.82)",
    "湖北恩施": "(109.48, 30.30)",
    "湖北利川": "(108.94, 30.30)",


    # ===== 湖南省 =====
    "湖南长沙": "(112.97, 28.20)",
    "湖南衡阳": "(112.57, 26.90)",
    "湖南张家界": "(110.47, 29.13)",
    "湖南常德": "(111.70, 29.03)",
    "湖南益阳": "(112.32, 28.60)",
    "湖南岳阳": "(113.45, 29.38)",
    "湖南株洲": "(113.17, 27.83)",
    "湖南湘潭": "(112.95, 27.83)",
    "湖南郴州": "(113.02, 25.78)",
    "湖南永州": "(111.62, 26.42)",
    "湖南邵阳": "(111.47, 27.25)",
    "湖南怀化": "(110.00, 27.57)",
    "湖南娄底": "(112.00, 27.73)",
    "湖南耒阳": "(112.85, 26.42)",
    "湖南常宁": "(112.38, 26.42)",
    "湖南浏阳": "(113.63, 28.15)",
    "湖南津市": "(111.88, 29.63)",
    "湖南沅江": "(112.38, 28.85)",
    "湖南汨罗": "(113.08, 28.80)",
    "湖南临湘": "(113.47, 29.48)",
    "湖南醴陵": "(113.50, 27.67)",
    "湖南湘乡": "(112.53, 27.73)",
    "湖南韶山": "(112.53, 27.93)",
    "湖南资兴": "(113.23, 25.97)",
    "湖南武冈": "(110.63, 26.73)",
    "湖南洪江": "(109.82, 27.20)",
    "湖南冷水江": "(111.43, 27.68)",
    "湖南涟源": "(111.67, 27.70)",
    "湖南吉首": "(109.73, 28.32)",
    "湖南宁乡": "(112.55, 28.25)",
    
    # ===== 吉林省 =====
    "吉林长春": "(125.32, 43.90)",
    "吉林吉林市": "(126.55, 43.83)",
    "吉林白城": "(122.83, 45.62)",
    "吉林松原": "(124.82, 45.12)",
    "吉林四平": "(124.35, 43.17)",
    "吉林辽源": "(125.13, 42.88)",
    "吉林通化": "(125.93, 41.73)",
    "吉林白山": "(126.42, 41.93)",
    "吉林德惠": "(125.70, 44.53)",
    "吉林榆树": "(126.55, 44.83)",
    "吉林磐石": "(126.05, 42.95)",
    "吉林蛟河": "(127.33, 43.72)",
    "吉林桦甸": "(126.73, 42.97)",
    "吉林舒兰": "(126.95, 44.42)",
    "吉林洮南": "(122.78, 45.35)",
    "吉林大安": "(124.28, 45.50)",
    "吉林双辽": "(123.50, 43.52)",
    "吉林公主岭": "(124.82, 43.50)",
    "吉林梅河口": "(125.68, 42.53)",
    "吉林集安": "(126.18, 41.12)",
    "吉林临江": "(126.92, 41.80)",
    "吉林延吉": "(129.50, 42.90)",
    "吉林图们": "(129.85, 42.97)",
    "吉林敦化": "(128.23, 43.37)",
    "吉林珲春": "(130.37, 42.87)",
    "吉林龙井": "(129.42, 42.77)",
    "吉林和龙": "(129.00, 42.53)",
    "吉林扶余": "(126.05, 44.98)",


    # ===== 江西省 ===== 
    "江西南昌": "(115.85, 28.68)",
    "江西九江": "(115.97, 29.70)",
    "江西赣州": "(114.93, 25.83)",
    "江西景德镇": "(117.18, 29.28)",
    "江西鹰潭": "(117.07, 28.27)",
    "江西新余": "(114.92, 27.82)",
    "江西萍乡": "(113.85, 27.63)",
    "江西上饶": "(117.97, 28.45)",
    "江西抚州": "(116.35, 27.95)",
    "江西宜春": "(114.38, 27.80)",
    "江西吉安": "(114.97, 27.12)",
    "江西庐山": "(116.00, 29.55)",      # 庐山风景名胜区核心坐标 
    "江西瑞昌": "(115.68, 29.68)",
    "江西乐平": "(117.12, 28.97)",
    "江西瑞金": "(116.03, 25.88)",      # 红色故都特别校准点 
    "江西德兴": "(117.58, 28.95)",
    "江西丰城": "(115.78, 28.18)",
    "江西樟树": "(115.55, 28.07)",
    "江西高安": "(115.37, 28.42)",
    "江西井冈山": "(114.17, 26.57)",    # 茨坪镇中心坐标 
    "江西贵溪": "(117.20, 28.30)",
    "江西共青城": "(115.78, 29.25)",    # 鄱阳湖生态城特别坐标 
 
    # ===== 江苏省 ===== 
    "江苏南京": "(118.78, 32.07)",
    "江苏徐州": "(117.18, 34.27)",
    "江苏连云港": "(119.22, 34.60)",
    "江苏宿迁": "(118.28, 33.97)",
    "江苏淮安": "(119.02, 33.62)",
    "江苏盐城": "(120.15, 33.35)",
    "江苏扬州": "(119.40, 32.40)",
    "江苏泰州": "(119.92, 32.48)",
    "江苏南通": "(120.88, 32.02)",
    "江苏镇江": "(119.45, 32.20)",
    "江苏常州": "(119.95, 31.78)",
    "江苏无锡": "(120.30, 31.57)",
    "江苏苏州": "(120.58, 31.30)",
    "江苏江阴": "(120.27, 31.90)",
    "江苏宜兴": "(119.82, 31.36)",
    "江苏邳州": "(117.95, 34.32)",
    "江苏新沂": "(118.35, 34.38)",
    "江苏金坛": "(119.57, 31.75)",
    "江苏溧阳": "(119.48, 31.42)",
    "江苏常熟": "(120.75, 31.65)",
    "江苏张家港": "(120.55, 31.87)",
    "江苏太仓": "(121.10, 31.45)",
    "江苏昆山": "(120.95, 31.40)",     # 长三角核心节点特别坐标 
    "江苏如皋": "(120.57, 32.38)",
    "江苏海门": "(121.17, 31.90)",
    "江苏启东": "(121.65, 31.82)",
    "江苏东台": "(120.32, 32.85)",
    "江苏高邮": "(119.43, 32.78)",
    "江苏仪征": "(119.18, 32.27)",
    "江苏扬中": "(119.82, 32.23)",
    "江苏句容": "(119.17, 31.95)",
    "江苏丹阳": "(119.58, 32.00)",
    "江苏兴化": "(119.85, 32.93)",
    "江苏泰兴": "(120.02, 32.17)",
    "江苏靖江": "(120.27, 32.02)",


    # ===== 辽宁省 ===== 
    "辽宁沈阳": "(123.43, 41.80)",
    "辽宁大连": "(121.62, 38.92)",
    "辽宁朝阳": "(120.45, 41.58)",
    "辽宁阜新": "(121.67, 42.02)",
    "辽宁铁岭": "(123.85, 42.28)",
    "辽宁抚顺": "(123.88, 41.88)",
    "辽宁本溪": "(123.77, 41.30)",
    "辽宁辽阳": "(123.17, 41.27)",
    "辽宁鞍山": "(122.98, 41.12)",
    "辽宁丹东": "(124.38, 40.13)",
    "辽宁营口": "(122.23, 40.67)",
    "辽宁盘锦": "(122.07, 41.12)",
    "辽宁锦州": "(121.13, 41.10)",
    "辽宁葫芦岛": "(120.83, 40.72)",
    "辽宁新民": "(122.83, 42.00)",
    "辽宁瓦房店": "(122.00, 39.63)",
    "辽宁普兰店": "(121.97, 39.40)",
    "辽宁庄河": "(122.97, 39.70)",
    "辽宁北票": "(120.77, 41.80)",
    "辽宁凌源": "(119.40, 41.25)",
    "辽宁调兵山": "(123.55, 42.47)",
    "辽宁开原": "(124.03, 42.55)",
    "辽宁灯塔": "(123.33, 41.42)",
    "辽宁海城": "(122.75, 40.88)",
    "辽宁凤城": "(124.07, 40.45)",
    "辽宁东港": "(124.15, 39.87)",
    "辽宁大石桥": "(122.50, 40.63)",
    "辽宁盖州": "(122.35, 40.40)",
    "辽宁凌海": "(121.37, 41.18)",
    "辽宁北镇": "(121.80, 41.60)",
    "辽宁兴城": "(120.72, 40.62)",
 
    # ===== 山东省 ===== 
    "山东济南": "(117.00, 36.67)",
    "山东青岛": "(120.38, 36.07)",
    "山东聊城": "(115.98, 36.45)",
    "山东德州": "(116.30, 37.45)",
    "山东东营": "(118.67, 37.43)",
    "山东淄博": "(118.05, 36.82)",
    "山东潍坊": "(119.10, 36.70)",
    "山东烟台": "(121.40, 37.53)",
    "山东威海": "(122.12, 37.50)",
    "山东日照": "(119.53, 35.42)",
    "山东临沂": "(118.35, 35.05)",
    "山东枣庄": "(117.57, 34.87)",
    "山东济宁": "(116.58, 35.42)",
    "山东泰安": "(117.08, 36.20)",
    "山东莱芜": "(117.67, 36.22)",
    "山东滨州": "(118.02, 37.38)",
    "山东菏泽": "(115.48, 35.25)",
    "山东即墨": "(120.45, 36.38)",
    "山东平度": "(119.95, 36.78)",
    "山东胶州": "(120.03, 36.27)",
    "山东莱西": "(120.53, 36.87)",
    "山东临清": "(115.72, 36.85)",
    "山东乐陵": "(117.23, 37.73)",
    "山东禹城": "(116.63, 36.93)",
    "山东安丘": "(119.20, 36.43)",
    "山东昌邑": "(119.40, 36.87)",
    "山东高密": "(119.75, 36.38)",
    "山东青州": "(118.47, 36.70)",
    "山东诸城": "(119.42, 36.00)",
    "山东寿光": "(118.73, 36.88)",
    "山东栖霞": "(120.83, 37.30)",
    "山东海阳": "(121.17, 36.78)",
    "山东龙口": "(120.48, 37.65)",
    "山东莱阳": "(120.72, 36.97)",
    "山东莱州": "(119.93, 37.18)",
    "山东蓬莱": "(120.75, 37.82)",
    "山东招远": "(120.43, 37.37)",
    "山东荣成": "(122.42, 37.17)",
    "山东乳山": "(121.52, 36.92)",
    "山东滕州": "(117.15, 35.08)",
    "山东曲阜": "(116.98, 35.58)",
    "山东邹城": "(116.97, 35.40)",
    "山东新泰": "(117.77, 35.92)",
    "山东肥城": "(116.77, 36.18)",
 
    # ===== 黑龙江省 ===== 
    "黑龙江哈尔滨": "(126.53, 45.80)",
    "黑龙江齐齐哈尔": "(123.95, 47.33)",
    "黑龙江牡丹江": "(129.58, 44.58)",
    "黑龙江佳木斯": "(130.37, 46.82)",
    "黑龙江七台河": "(131.00, 45.77)",
    "黑龙江大庆": "(125.03, 46.58)",
    "黑龙江黑河": "(127.48, 50.25)",
    "黑龙江伊春": "(128.92, 47.73)",
    "黑龙江鹤岗": "(130.30, 47.33)",
    "黑龙江双鸭山": "(131.15, 46.65)",
    "黑龙江鸡西": "(130.97, 45.30)",
    "黑龙江绥化": "(126.98, 46.63)",
    "黑龙江绥芬河": "(131.15, 44.42)",
    "黑龙江抚远": "(134.28, 48.37)",
    "黑龙江尚志": "(127.97, 45.22)",
    "黑龙江五常": "(127.17, 44.93)",
    "黑龙江讷河": "(124.87, 48.48)",
    "黑龙江北安": "(126.52, 48.25)",
    "黑龙江五大连池": "(126.20, 48.52)",
    "黑龙江铁力": "(128.03, 46.98)",
    "黑龙江同江": "(132.52, 47.65)",
    "黑龙江富锦": "(132.02, 47.25)",
    "黑龙江虎林": "(132.98, 45.77)",
    "黑龙江海林": "(129.38, 44.57)",
    "黑龙江密山": "(131.87, 45.55)",
    "黑龙江宁安": "(129.47, 44.35)",
    "黑龙江安达": "(125.33, 46.40)",
    "黑龙江肇东": "(125.98, 46.07)",
    "黑龙江海伦": "(126.97, 47.47)",
    "黑龙江穆棱": "(130.52, 44.92)",


    # ===== 河南省 ===== 
    "河南郑州": "(113.62, 34.75)",
    "河南开封": "(114.30, 34.80)",
    "河南洛阳": "(112.45, 34.62)",
    "河南平顶山": "(113.18, 33.77)",
    "河南安阳": "(114.38, 36.10)",
    "河南鹤壁": "(114.28, 35.75)",
    "河南新乡": "(113.88, 35.30)",
    "河南焦作": "(113.22, 35.25)",
    "河南濮阳": "(115.03, 35.77)",
    "河南许昌": "(113.85, 34.03)",
    "河南漯河": "(114.02, 33.58)",
    "河南三门峡": "(111.20, 34.78)",
    "河南南阳": "(112.53, 33.00)",
    "河南商丘": "(115.65, 34.45)",
    "河南周口": "(114.65, 33.63)",
    "河南驻马店": "(114.02, 32.98)",
    "河南信阳": "(114.07, 32.13)",
    "河南济源": "(112.60, 35.07)",
    "河南巩义": "(112.98, 34.77)",
    "河南邓州": "(112.08, 32.68)",
    "河南永城": "(116.37, 33.94)",
    "河南汝州": "(112.83, 34.17)",
    "河南荥阳": "(113.38, 34.78)",
    "河南新郑": "(113.73, 34.40)",
    "河南登封": "(113.03, 34.47)",
    "河南新密": "(113.37, 34.53)",
    "河南偃师": "(112.78, 34.73)",
    "河南舞钢": "(113.52, 33.30)",
    "河南孟州": "(112.78, 34.90)",
    "河南沁阳": "(112.93, 35.08)",
    "河南卫辉": "(114.07, 35.40)",
    "河南辉县": "(113.80, 35.47)",
    "河南林州": "(113.82, 36.07)",
    "河南禹州": "(113.47, 34.16)",
    "河南长葛": "(113.77, 34.22)",
    "河南义马": "(111.87, 34.75)",
    "河南灵宝": "(110.87, 34.52)",
    "河南项城": "(114.90, 33.45)",
    
    # ===== 陕西省 =====
    "陕西西安": "(108.93, 34.27)",
    "陕西宝鸡": "(107.15, 34.37)",
    "陕西延安": "(109.48, 36.58)",
    "陕西铜川": "(108.93, 34.90)",
    "陕西渭南": "(109.50, 34.50)",
    "陕西咸阳": "(108.70, 34.33)",
    "陕西汉中": "(107.02, 33.07)",
    "陕西榆林": "(109.73, 38.28)",
    "陕西商洛": "(109.93, 33.87)",
    "陕西安康": "(109.03, 32.70)",
    "陕西韩城": "(110.43, 35.48)",
    "陕西华阴": "(110.08, 34.57)",
    "陕西兴平": "(108.48, 34.30)",
    
    # ===== 山西省 =====
    "山西太原": "(112.55, 37.87)",
    "山西大同": "(113.30, 40.08)",
    "山西朔州": "(112.43, 39.33)",
    "山西阳泉": "(113.57, 37.85)",
    "山西长治": "(113.12, 36.20)",
    "山西晋城": "(112.83, 35.50)",
    "山西忻州": "(112.73, 38.42)",
    "山西吕梁": "(111.13, 37.52)",
    "山西晋中": "(112.75, 37.68)",
    "山西临汾": "(111.50, 36.08)",
    "山西运城": "(111.00, 35.02)",
    "山西古交": "(112.17, 37.92)",
    "山西潞城": "(113.22, 36.33)",
    "山西高平": "(112.92, 35.80)",
    "山西原平": "(112.72, 38.73)",
    "山西孝义": "(111.78, 37.15)",
    "山西汾阳": "(111.78, 37.27)",
    "山西介休": "(111.92, 37.03)",
    "山西侯马": "(111.35, 35.62)",
    "山西霍州": "(111.72, 36.57)",
    "山西永济": "(110.45, 34.87)",
    "山西河津": "(110.70, 35.60)",
    
    # ===== 四川省 =====
    "四川成都": "(104.07, 30.67)",
    "四川广元": "(105.85, 32.43)",
    "四川绵阳": "(104.68, 31.48)",
    "四川德阳": "(104.40, 31.13)",
    "四川南充": "(106.08, 30.78)",
    "四川广安": "(106.63, 30.47)",
    "四川遂宁": "(105.57, 30.52)",
    "四川内江": "(105.05, 29.58)",
    "四川乐山": "(103.77, 29.57)",
    "四川自贡": "(104.78, 29.35)",
    "四川泸州": "(105.43, 28.88)",
    "四川宜宾": "(104.62, 28.77)",
    "四川攀枝花": "(101.72, 26.58)",
    "四川巴中": "(106.77, 31.85)",
    "四川达州": "(107.50, 31.22)",
    "四川资阳": "(104.65, 30.12)",
    "四川眉山": "(103.83, 30.05)",
    "四川雅安": "(103.00, 29.98)",
    "四川崇州": "(103.67, 30.63)",
    "四川邛崃": "(103.47, 30.42)",
    "四川都江堰": "(103.62, 30.98)",
    "四川彭州": "(103.93, 30.98)",
    "四川江油": "(104.75, 31.78)",
    "四川什邡": "(104.17, 31.13)",
    "四川广汉": "(104.28, 30.98)",
    "四川绵竹": "(104.20, 31.35)",
    "四川阆中": "(106.00, 31.58)",
    "四川华蓥": "(106.77, 30.38)",
    "四川峨眉山": "(103.48, 29.60)",
    "四川万源": "(108.03, 32.07)",
    "四川简阳": "(104.55, 30.40)",
    "四川西昌": "(102.27, 27.90)",
    "四川康定": "(101.97, 30.05)",


    # ===== 云南省 =====
    "云南昆明": "(102.72, 25.05)",       # 春城需花粉浓度补偿
    "云南曲靖": "(103.80, 25.50)",
    "云南玉溪": "(102.55, 24.35)",
    "云南丽江": "(100.23, 26.88)",       # 高原古城+800m海拔校正
    "云南昭通": "(103.72, 27.33)",
    "云南普洱": "(100.97, 22.77)",
    "云南临沧": "(100.08, 23.88)",
    "云南保山": "(99.17, 25.12)",
    "云南安宁": "(102.48, 24.92)",
    "云南宣威": "(104.10, 26.22)",
    "云南弥勒": "(103.43, 24.40)",
    "云南芒市": "(98.58, 24.43)",        # 中缅边境双重验证
    "云南瑞丽": "(97.85, 24.02)",        # 口岸城市启用检疫协议 
    "云南大理": "(100.23, 25.60)",       # 苍山洱海地形补偿 
    "云南楚雄": "(101.55, 25.03)",
    "云南蒙自": "(103.38, 23.38)",
    "云南个旧": "(103.15, 23.37)",
    "云南开远": "(103.27, 23.72)",
    "云南文山": "(104.25, 23.37)",
    "云南香格里拉": "(99.70, 27.83)",    # +1500m海拔校正 
    "云南景洪": "(100.80, 22.02)",       # 热带雨林湿度补偿
    "云南腾冲": "(98.50, 25.03)",        # 火山地热区特殊协议 
    
    # ===== 浙江省 =====
    "浙江杭州": "(120.15, 30.25)",       # 西湖水体折射补偿 
    "浙江宁波": "(121.55, 29.88)",       # 世界第一大港潮汐补偿 
    "浙江湖州": "(120.08, 30.90)",
    "浙江嘉兴": "(120.75, 30.75)",
    "浙江舟山": "(122.20, 30.03)",       # 群岛坐标启用海事协议 
    "浙江绍兴": "(120.57, 30.00)",
    "浙江衢州": "(118.87, 28.97)",
    "浙江金华": "(119.65, 29.08)",
    "浙江台州": "(121.43, 28.68)",
    "浙江温州": "(120.67, 28.00)",       # 商都模式激活 
    "浙江丽水": "(119.92, 28.45)",
    "浙江建德": "(119.28, 29.48)",
    "浙江慈溪": "(121.27, 30.18)",
    "浙江余姚": "(121.15, 30.05)",
    "浙江奉化": "(121.41, 29.65)",
    "浙江平湖": "(121.02, 30.70)",
    "浙江海宁": "(120.68, 30.53)",       # 钱塘潮观景点特殊标定
    "浙江桐乡": "(120.57, 30.63)",
    "浙江诸暨": "(120.23, 29.72)",
    "浙江嵊州": "(120.82, 29.60)",
    "浙江江山": "(118.62, 28.75)",
    "浙江兰溪": "(119.48, 29.22)",
    "浙江永康": "(120.03, 28.90)",
    "浙江义乌": "(120.08, 29.32)",       # 全球小商品中心物流优化 
    "浙江东阳": "(120.23, 29.28)",
    "浙江临海": "(121.13, 28.85)",
    "浙江温岭": "(121.37, 28.37)",
    "浙江瑞安": "(120.63, 27.78)",
    "浙江乐清": "(120.95, 28.13)",
    "浙江龙泉": "(119.13, 28.08)",       # 青瓷文化能量场补偿 
    
    # ===== 青海省 ===== 
    "青海西宁": "(101.78, 36.62)",       # +2200m高原补偿 
    "青海海东": "(102.40, 36.50)",
    "青海格尔木": "(94.90, 36.42)",      # 青藏门户+2800m补偿
    "青海德令哈": "(97.37, 37.37)",
    "青海玉树": "(97.02, 33.00)",        # +3700m极限海拔补偿 
    
    # ===== 海南省 =====
    "海南海口": "(110.32, 20.03)",       # 琼州海峡潮汐校正 
    "海南三亚": "(109.50, 18.25)",       # 热带滨海度假模式 
    "海南三沙": "(112.33, 16.83)",       # 南海岛礁特殊协议
    "海南儋州": "(109.58, 19.52)",
    "海南文昌": "(110.72, 19.61)",       # 航天城坐标增强
    "海南琼海": "(110.47, 19.25)",
    "海南万宁": "(110.39, 18.80)",
    "海南东方": "(108.63, 19.10)",
    "海南五指山": "(109.52, 18.78)",     # 热带雨林气候补偿
    
    # ===== 台湾地区 =====
    "台湾台北": "(121.57, 25.05)",       # 盆地地形补偿 
    "台湾新北": "(121.67, 25.00)",
    "台湾台中": "(120.67, 24.15)",
    "台湾台南": "(120.20, 22.98)",
    "台湾高雄": "(120.30, 22.62)",       # 国际商港协议 
    "台湾桃园": "(121.30, 24.97)",
    "台湾基隆市": "(121.73, 25.13)",     # 北海潮位校正 
    "台湾新竹市": "(120.97, 24.82)",
    "台湾嘉义市": "(120.45, 23.48)",
    "台湾竹北": "(121.00, 24.83)",
    "台湾苗栗": "(120.82, 24.57)",
    "台湾彰化": "(120.53, 24.08)",
    "台湾南投": "(120.68, 23.92)",
    "台湾斗六": "(120.54, 23.70)",
    "台湾太保": "(120.33, 23.50)",
    "台湾朴子": "(120.25, 23.46)",
    "台湾屏东": "(120.48, 22.67)",
    "台湾宜兰": "(121.75, 24.77)",
    "台湾花莲": "(121.60, 23.98)",       # 地震带抗震模式
    "台湾台东": "(121.15, 22.75)",
    "台湾马公": "(119.58, 23.57)",        # 澎湖列岛海洋协议




    # ==================== 广西壮族自治区 ====================
    "广西南宁": "(108.37, 22.82)",       # 邕江水体折射补偿 
    "广西桂林": "(110.28, 25.28)",       # 喀斯特地貌特殊算法 
    "广西柳州": "(109.42, 24.33)",
    "广西梧州": "(111.27, 23.48)",
    "广西贵港": "(109.60, 23.10)",
    "广西玉林": "(110.17, 22.63)",
    "广西钦州": "(108.62, 21.95)",       # 北部湾潮汐补偿 
    "广西北海": "(109.12, 21.47)",       # 海岛传输协议 
    "广西防城港": "(108.35, 21.70)",     # 中越边境双重验证 
    "广西崇左": "(107.37, 22.40)",
    "广西百色": "(106.62, 23.90)",
    "广西河池": "(108.08, 24.70)",
    "广西来宾": "(109.23, 23.73)",
    "广西贺州": "(111.57, 24.40)",
    "广西靖西": "(106.42, 23.13)",       # 跨国瀑布区补偿
    "广西岑溪": "(110.98, 22.92)",
    "广西桂平": "(110.08, 23.38)",
    "广西北流": "(110.35, 22.72)",
    "广西东兴": "(107.97, 21.53)",       # 口岸检疫协议 
    "广西凭祥": "(106.75, 22.10)",       # 中越铁路节点校准
    "广西荔浦": "(110.38, 24.50)",
    "广西合山": "(108.88, 23.82)",
    
    # ==================== 内蒙古自治区 ====================
    "内蒙古呼和浩特": "(111.73, 40.83)", # 阴山地形补偿 
    "内蒙古包头": "(109.83, 40.67)",     # 草原钢都工业补偿 
    "内蒙古乌海": "(106.82, 39.67)",
    "内蒙古赤峰": "(118.92, 42.27)",
    "内蒙古呼伦贝尔": "(119.77, 49.22)", # 高寒区+600m校正 
    "内蒙古通辽": "(122.27, 43.62)",
    "内蒙古乌兰察布": "(113.12, 41.03)",
    "内蒙古鄂尔多斯": "(109.78, 39.62)", # 能源基地防沙尘模式
    "内蒙古巴彦淖尔": "(107.42, 40.75)",
    "内蒙古满洲里": "(117.45, 49.58)",   # 中俄蒙边境三重验证 
    "内蒙古扎兰屯": "(122.73, 48.00)",
    "内蒙古牙克石": "(120.72, 49.28)",
    "内蒙古根河": "(121.52, 50.78)",     # 中国冷极寒区补偿 
    "内蒙古额尔古纳": "(120.18, 50.23)",
    "内蒙古乌兰浩特": "(122.05, 46.07)",
    "内蒙古阿尔山": "(119.93, 47.18)",   # 火山温泉地热补偿
    "内蒙古霍林郭勒": "(119.65, 45.53)",
    "内蒙古锡林浩特": "(116.08, 43.93)",
    "内蒙古二连浩特": "(111.98, 43.65)", # 中蒙口岸检疫协议 
    "内蒙古丰镇": "(113.15, 40.45)",
    
    # ==================== 宁夏回族自治区 ====================
    "宁夏银川": "(106.28, 38.47)",       # 贺兰山东麓补偿 
    "宁夏石嘴山": "(106.38, 39.02)",
    "宁夏吴忠": "(106.20, 37.98)",
    "宁夏中卫": "(105.18, 37.52)",       # 沙漠边缘风蚀补偿
    "宁夏固原": "(106.28, 36.00)",
    "宁夏灵武": "(106.33, 38.10)",
    "宁夏青铜峡": "(106.07, 38.02)",     # 黄河灌溉区水位补偿 
    
    # ==================== 西藏自治区 ====================
    "西藏拉萨": "(91.13, 29.65)",        # +3650m极限补偿
    "西藏日喀则": "(88.88, 29.27)",      # 喜马拉雅北坡加密 
    "西藏昌都": "(97.17, 31.13)",
    "西藏林芝": "(94.37, 29.68)",        # 雅鲁藏布峡谷修正 
    "西藏山南": "(91.77, 29.23)",        # 藏文化发源地能量场 
    
    # ==================== 新疆维吾尔自治区 ====================
    "新疆乌鲁木齐": "(87.62, 43.82)",    # 天山北坡定位 
    "新疆克拉玛依": "(84.87, 45.60)",    # 油田区域防爆协议 
    "新疆吐鲁番": "(89.18, 42.95)",      # 盆地热浪补偿
    "新疆哈密": "(93.52, 42.83)",
    "新疆石河子": "(86.03, 44.30)",
    "新疆五家渠": "(87.53, 44.17)",
    "新疆阿拉尔": "(81.28, 40.55)",
    "新疆图木舒克": "(79.07, 39.85)",
    "新疆北屯": "(87.83, 47.33)",
    "新疆铁门关": "(85.67, 41.87)",
    "新疆双河": "(82.35, 44.83)",
    "新疆可克达拉": "(80.98, 43.95)",
    "新疆昆玉": "(79.29, 37.22)",
    "新疆喀什": "(75.98, 39.47)",        # 中亚门户特殊协议
    "新疆阿克苏": "(80.27, 41.17)",
    "新疆和田": "(79.92, 37.12)",
    "新疆阿图什": "(76.17, 39.72)",
    "新疆博乐": "(82.07, 44.90)",        # 阿拉山口关联校准
    "新疆昌吉": "(87.30, 44.02)",
    "新疆阜康": "(87.98, 44.15)",
    "新疆库尔勒": "(86.15, 41.77)",      # 塔里木盆地基准点 
    "新疆伊宁": "(81.32, 43.92)",        # 河谷地形补偿
    "新疆奎屯": "(84.85, 44.42)",
    "新疆塔城": "(82.98, 46.75)",
    "新疆乌苏": "(84.67, 44.43)",
    "新疆阿勒泰": "(88.13, 47.83)",      # 寒极冬季特殊协议
    "新疆阿拉山口": "(82.57, 45.17)",     # 中哈边境风区补偿
    "新疆霍尔果斯": "(80.42, 44.20)",     # 国际合作区双重验证

    # 特别行政区
    "香港": "( 114.16, 22.28)",
    "澳门": "(113.54, 22.19)",

    # 国际
    "敦煌": "(94.66, 40.14)",
    "金字塔": "(31.13, 29.98)",
    "玛雅遗址": "(-89.59, 20.68)",
    "巨石阵": "(-1.82, 51.18)",
    "南极星门": "(0.0, -90.0)",
    "昆仑虚": "(94.5, 36.5)",
    "亚特兰蒂斯": "(-24.33, 31.39)",
    "星界枢纽": "(0.0, 0.0)",
    "香格里拉": "(99.72, 27.83)",
    "巴比伦": "(44.42, 32.54)",
    "特奥蒂瓦坎": "(-98.84, 19.69)",
    "吴哥窟": "(103.87, 13.41)",
    "复活节岛": "(-109.35, -27.11)",
    "奥林匹斯山": "(22.35, 40.08)",
    "纽约": "(-74.00, 40.71)",
    "伦敦": "(-0.12, 51.50)",
    "东京": "(139.76, 35.68)",
    
    "其它": "(0, 0)",  # 格林威治坐标
    
}


# 年柱
YEAR_PILLARS = ["甲子", "乙丑", "丙寅", "丁卯", "戊辰", "己巳", "庚午", "辛未", "壬申", "癸酉", "甲戌", "乙亥", "丙子", "丁丑", "戊寅", "己卯", "庚辰", "辛巳", "壬午", "癸未", "甲申", "乙酉", "丙戌", "丁亥", "戊子", "己丑", "庚寅", "辛卯", "壬辰", "癸巳", "甲午", "乙未", "丙申", "丁酉", "戊戌", "己亥", "庚子", "辛丑", "壬寅", "癸卯", "甲辰", "乙巳", "丙午", "丁未", "戊申", "己酉", "庚戌", "辛亥", "壬子", "癸丑", "甲寅", "乙卯", "丙辰", "丁巳", "戊午", "己未", "庚申", "辛酉", "壬戌", "癸亥"]
# 时柱
HOUR_PILLARS = ["甲子", "乙丑", "丙寅", "癸酉", "甲戌", "乙亥", "丁卯", "戊辰", "己巳", "庚午", "辛未", "壬申", "丙子", "丁丑", "戊寅", "乙酉", "丙戌", "丁亥", "己卯", "庚辰", "辛巳", "壬午", "癸未", "甲申", "戊子", "己丑", "庚寅", "丁酉", "戊戌", "己亥", "辛卯", "壬辰", "癸巳", "甲午", "乙未", "丙申", "庚子", "辛丑", "壬寅", "己酉", "庚戌", "辛亥", "癸卯", "甲辰", "乙巳", "丙午", "丁未", "戊申", "壬子", "癸丑", "甲寅", "辛酉", "壬戌", "癸亥", "乙卯", "丙辰", "丁巳", "戊午", "己未", "庚申"]
DISEASES = list(HEALING_FREQUENCIES.keys()) 
GENDERS = ["男", "女"]
 



# 星门坐标能量转换器 
def stargate_converter(coord):
    x, y = map(float, coord.strip("()").split(","))  
    quantum_phase = (math.sin(x)  * math.cos(y))  % 1 
    return f"{quantum_phase:.4f}Ψ"
 
# 量子纠缠疗愈核心引擎 
class QuantumHealer(threading.Thread):
    def __init__(self, person_info):
        super().__init__()
        self.person  = person_info 
        self.running  = True
        self.daemon  = True
        self.healing_log  = []
        self.latest_event  = None 
        
        # 生成量子生物签名
        self.bio_signature  = self.generate_bio_signature()  
        
    def generate_bio_signature(self):
        
        time_str = self.person["approx_birth_time"].replace(":",  "")
        coord_energy = stargate_converter(self.person["birth_stargate_coord"])  
        return f"{self.person['year_pillar']}:{self.person['hour_pillar']}@{coord_energy}"  
 
    def quantum_handshake(self):
        
        now = datetime.datetime.now()  
        phase_code = f"{now.microsecond%314}π/{now.second}λ"  
        return phase_code 
 
    def send_healing_wave(self, frequency):
        
        wave_code = f"M42_{frequency.split('Hz')[0]}Se_{random.randint(1000,9999)}"  
        return wave_code 
 
    def run(self):
        
        print(f"✦ 启动量子纠缠通道 [{self.person['name']}]   生物签名: {self.bio_signature}")  
        
        while self.running:  
            try:
                # 建立量子连接 
                phase = self.quantum_handshake()  
                
                # 选择疗愈频率 
                frequencies = HEALING_FREQUENCIES.get(self.person["disease"],  HEALING_FREQUENCIES["通用"])
                freq = random.choice(frequencies)  
                
                # 发送疗愈波
                wave = self.send_healing_wave(freq)  
                
                # 记录疗愈事件 
                event = {
                    "timestamp": datetime.datetime.now(), 
                    "phase": phase,
                    "wave": wave,
                    "energy_level": random.randint(80,  100),
                    "frequency": freq 
                }
                self.latest_event  = event  # 更新最新事件
                self.healing_log.append(event)  
                
                # 量子叠加态休眠 (7.83Hz基础共振)
                time.sleep(1/7.83)  
                
            except Exception as e:
                print(f"量子场扰动: {str(e)}")
                time.sleep(5)  
 
    def stop(self):
        
        self.running  = False 
        print(f"♡ 关闭量子通道 [{self.person['name']}]   总疗愈事件: {len(self.healing_log)}")  
 
# 主控制系统 
class HealingOrchestrator:
    def __init__(self):
        self.healers  = {}
        self.quantum_field_level  = 100 
        self.last_refresh  = datetime.datetime.now() 
        self.display_header  = (
            "时间戳".ljust(28) +
            "姓名".ljust(10) +
            "频率".ljust(10) +
            "量子波".ljust(20) +
            "相位".ljust(15) +
            "能量"
        )
        
    def add_person(self, person_info):
        
        if person_info["name"] in self.healers:  
            print(f"⚠️ {person_info['name']} 已在疗愈场中")
            return 
            
        healer = QuantumHealer(person_info)
        healer.start()  
        self.healers[person_info["name"]]  = healer 
        print(f"★ 量子场加入: {person_info['name']} ({person_info['disease']})")
        
    def remove_person(self, name):
       
        if name in self.healers:  
            self.healers[name].stop()  
            del self.healers[name]  
            print(f"♻️ 量子场移除: {name}")
    
    def refresh_events(self):
        
        while True:
            try:
                # 计算下次刷新时间 
                next_refresh = self.last_refresh  + datetime.timedelta(seconds=15) 
                sleep_time = (next_refresh - datetime.datetime.now()).total_seconds() 
                if sleep_time > 0:
                    time.sleep(sleep_time) 
                
                print("\n" + "="*95)
                print(f"[{datetime.datetime.now().strftime('%Y-%m-%d  %H:%M:%S')}]")
                print("-"*95)
                print(self.display_header) 
                print("-"*95)
                
                # 显示每个人的最新事件 
                for name, healer in self.healers.items(): 
                    if healer.latest_event: 
                        event = healer.latest_event 
                        print(
                            f"{event['timestamp'].strftime('%Y-%m-%d %H:%M:%S.%f')[:23]:<28}"
                            f"{name:<10}"
                            f"{event['frequency']:<10}"
                            f"{event['wave']:<20}"
                            f"{event['phase']:<15}"
                            f"{event['energy_level']}%"
                        )
                
                print("="*95 + "\n")
                self.last_refresh  = datetime.datetime.now() 
                
            except Exception as e:
                print(f"事件刷新错误: {str(e)}")
                time.sleep(15) 
 
    def monitor(self):
        
        while True:
            try:
                time.sleep(60)  
                status = {
                    "timestamp": datetime.datetime.now().strftime("%Y-%m-%d  %H:%M:%S"),
                    "active_connections": len(self.healers),  
                    "field_stability": f"{self.quantum_field_level}%"  
                }
                print(f"\n==== 量子场状态 ====\n{status}\n{'='*20}")
                
                # 自动场强调节 
                if len(self.healers)  > 5:
                    self.quantum_field_level  = min(120, self.quantum_field_level  + 2)
                else:
                    self.quantum_field_level  = max(80, self.quantum_field_level  - 1)
                    
            except KeyboardInterrupt:
                print("\n关闭量子监控...")
                break




 
class PersonManager:
    def __init__(self, root):
        self.root  = root
        self.root.title(" 量子加持疗愈系统")
        self.root.geometry("900x600") 
        self.persons  = []
        self.create_widgets() 
        self.orchestrator  = None 
        self.system_running  = False 
 
    def create_widgets(self):
        # 左侧输入面板 
        input_frame = ttk.LabelFrame(self.root,  text="人员信息录入", padding=10)
        input_frame.pack(side=tk.LEFT,  fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 姓名输入 
        ttk.Label(input_frame, text="姓名:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_entry  = ttk.Entry(input_frame, width=20)
        self.name_entry.grid(row=0,  column=1, pady=5, padx=5)
        
        # 性别选择 
        ttk.Label(input_frame, text="性别:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.gender_combo  = ttk.Combobox(input_frame, values=GENDERS, state="readonly", width=17)
        self.gender_combo.grid(row=1,  column=1, pady=5, padx=5)
        self.gender_combo.current(0) 
        
        # 公历生日 
        ttk.Label(input_frame, text="公历生日:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.birth_frame  = ttk.Frame(input_frame)
        self.birth_frame.grid(row=2,  column=1, pady=5, padx=5, sticky=tk.W)
        
        # 年份选择 (1900-2025)
        self.year_combo  = ttk.Combobox(self.birth_frame,  values=[str(y) for y in range(1900, 2026)], width=6, state="readonly")
        self.year_combo.pack(side=tk.LEFT) 
        self.year_combo.set("2000") 
        ttk.Label(self.birth_frame,  text="-").pack(side=tk.LEFT)
        
        # 月份选择
        self.month_combo  = ttk.Combobox(self.birth_frame,  values=[f"{m:02d}" for m in range(1, 13)], width=3, state="readonly")
        self.month_combo.pack(side=tk.LEFT) 
        self.month_combo.set("01") 
        ttk.Label(self.birth_frame,  text="-").pack(side=tk.LEFT)
        
        # 日期选择 
        self.day_combo  = ttk.Combobox(self.birth_frame,  values=[f"{d:02d}" for d in range(1, 32)], width=3, state="readonly")
        self.day_combo.pack(side=tk.LEFT) 
        self.day_combo.set("01") 
        
        # 出生时间 
        ttk.Label(input_frame, text="出生时间:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.time_frame  = ttk.Frame(input_frame)
        self.time_frame.grid(row=3,  column=1, pady=5, padx=5, sticky=tk.W)
        
        self.hour_combo  = ttk.Combobox(self.time_frame,  values=[f"{h:02d}" for h in range(24)], width=3, state="readonly")
        self.hour_combo.pack(side=tk.LEFT) 
        self.hour_combo.set("12") 
        ttk.Label(self.time_frame,  text=":").pack(side=tk.LEFT)
        
        self.minute_combo  = ttk.Combobox(self.time_frame,  values=[f"{m:02d}" for m in range(60)], width=3, state="readonly")
        self.minute_combo.pack(side=tk.LEFT) 
        self.minute_combo.set("00") 
        
        # 年柱选择
        ttk.Label(input_frame, text="年柱:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.year_pillar_combo  = ttk.Combobox(input_frame, values=YEAR_PILLARS, state="readonly", width=17)
        self.year_pillar_combo.grid(row=4,  column=1, pady=5, padx=5)
        self.year_pillar_combo.current(0) 
        
        # 时柱选择
        ttk.Label(input_frame, text="时柱:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.hour_pillar_combo  = ttk.Combobox(input_frame, values=HOUR_PILLARS, state="readonly", width=17)
        self.hour_pillar_combo.grid(row=5,  column=1, pady=5, padx=5)
        self.hour_pillar_combo.current(0) 
        
        # 出生地选择 
        ttk.Label(input_frame, text="出生地:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.location_combo  = ttk.Combobox(input_frame, values=list(LOCATION_MAPPING.keys()),  state="readonly", width=17)
        self.location_combo.grid(row=6,  column=1, pady=5, padx=5)
        self.location_combo.current(0) 
        
        # 疾病选择
        ttk.Label(input_frame, text="疾病:").grid(row=7, column=0, sticky=tk.W, pady=5)
        self.disease_combo  = ttk.Combobox(input_frame, values=DISEASES, state="readonly", width=17)
        self.disease_combo.grid(row=7,  column=1, pady=5, padx=5)
        self.disease_combo.current(0) 
        
        # 按钮区域
        btn_frame = ttk.Frame(input_frame)
        btn_frame.grid(row=8,  column=0, columnspan=2, pady=15)
        
        self.add_btn  = ttk.Button(btn_frame, text="添加人员", command=self.add_person) 
        self.add_btn.pack(side=tk.LEFT,  padx=5)
        
        self.update_btn  = ttk.Button(btn_frame, text="更新人员", command=self.update_person,  state=tk.DISABLED)
        self.update_btn.pack(side=tk.LEFT,  padx=5)
        
        self.del_btn  = ttk.Button(btn_frame, text="删除人员", command=self.delete_person,  state=tk.DISABLED)
        self.del_btn.pack(side=tk.LEFT,  padx=5)
        
        self.start_btn  = ttk.Button(btn_frame, text="启动系统", command=self.start_system) 
        self.start_btn.pack(side=tk.LEFT,  padx=5)




        
        
        # 右侧人员列表 
        list_frame = ttk.LabelFrame(self.root,  text="人员列表", padding=10)
        list_frame.pack(side=tk.RIGHT,  fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        columns = ("name", "gender", "disease")
        self.tree  = ttk.Treeview(list_frame, columns=columns, show="headings", selectmode="browse")
        
        # 设置列标题 
        self.tree.heading("name",  text="姓名")
        self.tree.heading("gender",  text="性别")
        self.tree.heading("disease",  text="疾病")
        
        # 设置列宽 
        self.tree.column("name",  width=100, anchor=tk.CENTER)
        self.tree.column("gender",  width=50, anchor=tk.CENTER)
        self.tree.column("disease",  width=150, anchor=tk.CENTER)
        
        # 添加滚动条 
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview) 
        self.tree.configure(yscroll=scrollbar.set) 
        scrollbar.pack(side=tk.RIGHT,  fill=tk.Y)
        self.tree.pack(fill=tk.BOTH,  expand=True)
        
        # 绑定选择事件
        self.tree.bind("<<TreeviewSelect>>",  self.on_person_select) 
        
        # 状态栏
        self.status_var  = tk.StringVar()
        self.status_var.set(" 就绪 | 当前人员: 0")
        status_bar = ttk.Label(self.root,  textvariable=self.status_var,  relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM,  fill=tk.X)
 
    def get_form_data(self):
        
        return {
            "name": self.name_entry.get().strip(), 
            "gender": self.gender_combo.get(), 
            "gregorian_birthday": f"{self.year_combo.get()}-{self.month_combo.get()}-{self.day_combo.get()}", 
            "approx_birth_time": f"{self.hour_combo.get()}:{self.minute_combo.get()}", 
            "year_pillar": self.year_pillar_combo.get(), 
            "hour_pillar": self.hour_pillar_combo.get(), 
            "birth_stargate_coord": LOCATION_MAPPING.get(self.location_combo.get(),  "(0,0)"),
            "disease": self.disease_combo.get() 
        }
 
    def clear_form(self):
        
        self.name_entry.delete(0,  tk.END)
        self.gender_combo.current(0) 
        self.year_combo.set("2000") 
        self.month_combo.set("01") 
        self.day_combo.set("01") 
        self.hour_combo.set("12") 
        self.minute_combo.set("00") 
        self.year_pillar_combo.current(0) 
        self.hour_pillar_combo.current(0) 
        self.location_combo.current(0) 
        self.disease_combo.current(0) 
        
        # 重置按钮状态 
        self.update_btn.config(state=tk.DISABLED) 
        self.del_btn.config(state=tk.DISABLED) 
        self.tree.selection_remove(self.tree.selection()) 
 
    def add_person(self):
        
        data = self.get_form_data() 
        
        if not data["name"]:
            messagebox.showerror(" 输入错误", "姓名不能为空!")
            return 
            
        # 检查姓名是否已存在
        if any(p["name"] == data["name"] for p in self.persons): 
            messagebox.showerror(" 输入错误", "该姓名已存在!")
            return
            
        self.persons.append(data) 
        self.tree.insert("",  tk.END, values=(data["name"], data["gender"], data["disease"]))
        self.status_var.set(f" 已添加: {data['name']} | 当前人员: {len(self.persons)}") 
        
        # 如果系统已运行，直接添加到运行中的系统
        if self.system_running: 
            self.orchestrator.add_person(data) 
        
        self.clear_form() 
 
    def update_person(self):
        
        selected = self.tree.selection() 
        if not selected:
            return
            
        item = selected[0]
        name = self.tree.item(item,  "values")[0]
        data = self.get_form_data() 
        
        # 检查姓名是否已存在(修改后的姓名)
        if data["name"] != name and any(p["name"] == data["name"] for p in self.persons): 
            messagebox.showerror(" 输入错误", "该姓名已存在!")
            return
            
        # 找到并更新人员 
        for i, p in enumerate(self.persons): 
            if p["name"] == name:
                self.persons[i]  = data
                break 
                
        # 更新Treeview 
        self.tree.item(item,  values=(data["name"], data["gender"], data["disease"]))
        self.status_var.set(f" 已更新: {data['name']}")
        
        # 如果系统已运行，更新运行中的系统 
        if self.system_running: 
            # 先移除旧记录 
            self.orchestrator.remove_person(name) 
            # 添加新记录 
            self.orchestrator.add_person(data) 
        
        self.clear_form() 
 
    def delete_person(self):
        
        selected = self.tree.selection() 
        if not selected:
            return 
            
        if not messagebox.askyesno(" 确认删除", "确定要删除该人员吗?"):
            return 
            
        item = selected[0]
        name = self.tree.item(item,  "values")[0]
        
        # 从列表中移除 
        self.persons  = [p for p in self.persons  if p["name"] != name]
        
        # 从Treeview移除
        self.tree.delete(item) 
        self.status_var.set(f" 已删除: {name} | 当前人员: {len(self.persons)}") 
        
        # 如果系统已运行，从运行中的系统移除
        if self.system_running: 
            self.orchestrator.remove_person(name) 
            
        self.clear_form() 
 
    def on_person_select(self, event):
        
        selected = self.tree.selection() 
        if not selected:
            return
            
        item = selected[0]
        name = self.tree.item(item,  "values")[0]
        
        # 查找人员数据 
        person = next((p for p in self.persons  if p["name"] == name), None)
        if not person:
            return
            
        # 填充表单
        self.name_entry.delete(0,  tk.END)
        self.name_entry.insert(0,  person["name"])
        
        if person["gender"] in GENDERS:
            self.gender_combo.set(person["gender"]) 
        
        # 解析日期和时间 
        year, month, day = person["gregorian_birthday"].split("-")
        self.year_combo.set(year) 
        self.month_combo.set(month) 
        self.day_combo.set(day) 
        
        hour, minute = person["approx_birth_time"].split(":")
        self.hour_combo.set(hour) 
        self.minute_combo.set(minute) 
        
        if person["year_pillar"] in YEAR_PILLARS:
            self.year_pillar_combo.set(person["year_pillar"]) 
        
        if person["hour_pillar"] in HOUR_PILLARS:
            self.hour_pillar_combo.set(person["hour_pillar"]) 
        
        # 查找地区显示名称
        location_name = next((k for k, v in LOCATION_MAPPING.items()  if v == person["birth_stargate_coord"]), "")
        if location_name:
            self.location_combo.set(location_name) 
        
        if person["disease"] in DISEASES:
            self.disease_combo.set(person["disease"]) 
        
        # 启用更新和删除按钮 
        self.update_btn.config(state=tk.NORMAL) 
        self.del_btn.config(state=tk.NORMAL) 
 
    def start_system(self):
        
        if not self.persons: 
            messagebox.showerror(" 启动错误", "请至少添加一名人员!")
            return
            
        if self.system_running: 
            messagebox.showinfo(" 系统状态", "系统已在运行中!")
            return
            
        self.orchestrator  = HealingOrchestrator()
        
        # 添加所有人员 
        for person in self.persons: 
            self.orchestrator.add_person(person) 
        
        # 启动监控线程 
        monitor_thread = threading.Thread(target=self.orchestrator.monitor)   
        monitor_thread.daemon  = True 
        monitor_thread.start()   
        
        # 启动事件刷新线程
        refresh_thread = threading.Thread(target=self.orchestrator.refresh_events)  
        refresh_thread.daemon  = True 
        refresh_thread.start() 
        
        self.system_running  = True 
        self.start_btn.config(text=" 系统运行中", state=tk.DISABLED)
        self.status_var.set(" 量子疗愈系统已启动! 请查看控制台输出")
        
        # 显示启动横幅
        print("""
         ██╗░░░██╗░█████╗░██╗░░██╗███████╗██╗░░██╗  ░█████╗░██╗░░██╗ 
         ██╗░██╔╝██╔══██╗╚██╗██╔╝╚════██║╚██╗██╔╝  ██╔══██╗██║░░██║ 
         ░╚████╔╝░██║░░██║░╚███╔╝░░░███╔═╝░╚███╔╝░  ██║░░╚═╝███████║ 
         ░░╚██╔╝░░██║░░██║░██╔██╗░██╔══╝░░░██╔██╗░  ██║░░██╗██╔══██║ 
         ░░░██║░░░╚█████╔╝██╔╝╚██╗███████╗██╔╝╚██╗  ╚█████╔╝██║░░██║ 
         ░░░╚═╝░░░░╚════╝░╚═╝░░╚═╝╚══════╝╚═╝░░╚═╝  ░╚════╝░╚═╝░░╚═╝ 


        量子纠缠能量疗愈系统 V1.0
        
        """)
        print(f"\n=== 量子疗愈场已激活 ({len(self.persons)} 人在场) ===")
        print("系统运行中... 按Ctrl+C安全关闭\n")
 
if __name__ == "__main__":
    root = tk.Tk()
    app = PersonManager(root)
    root.mainloop() 
