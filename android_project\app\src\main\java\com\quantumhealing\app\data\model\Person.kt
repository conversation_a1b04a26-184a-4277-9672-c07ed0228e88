package com.quantumhealing.app.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 人员信息数据模型
 * 对应原Python程序中的person_info字典
 */
@Entity(tableName = "persons")
data class Person(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    val name: String,                    // 姓名
    val gender: String,                  // 性别
    val gregorianBirthday: String,       // 公历生日 (YYYY-MM-DD)
    val approxBirthTime: String,         // 出生时间 (HH:MM)
    val yearPillar: String,              // 年柱
    val hourPillar: String,              // 时柱
    val birthStargateCoord: String,      // 出生地星门坐标
    val disease: String,                 // 疾病
    val isActive: Boolean = false        // 是否在疗愈中
) {
    /**
     * 生成量子生物签名
     * 对应原程序的generate_bio_signature方法
     */
    fun generateBioSignature(): String {
        val timeStr = approxBirthTime.replace(":", "")
        val coordEnergy = stargateConverter(birthStargateCoord)
        return "$yearPillar:$hourPillar@$coordEnergy"
    }
    
    /**
     * 星门坐标能量转换器
     * 对应原程序的stargate_converter函数
     */
    private fun stargateConverter(coord: String): String {
        val cleanCoord = coord.trim('(', ')')
        val parts = cleanCoord.split(",")
        if (parts.size != 2) return "0.0000Ψ"
        
        try {
            val x = parts[0].trim().toDouble()
            val y = parts[1].trim().toDouble()
            val quantumPhase = (kotlin.math.sin(x) * kotlin.math.cos(y)) % 1
            return String.format("%.4fΨ", quantumPhase)
        } catch (e: Exception) {
            return "0.0000Ψ"
        }
    }
    
    /**
     * 获取显示用的简短信息
     */
    fun getDisplayInfo(): String {
        return "$name ($gender) - $disease"
    }
}
