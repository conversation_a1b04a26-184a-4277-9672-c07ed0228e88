<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.quantumhealing.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_main_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="335" endOffset="14"/></Target><Target id="@+id/etName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="49" startOffset="24" endLine="53" endOffset="64"/></Target><Target id="@+id/spinnerGender" view="Spinner"><Expressions/><location startLine="64" startOffset="20" endLine="68" endOffset="59"/></Target><Target id="@+id/spinnerYear" view="Spinner"><Expressions/><location startLine="97" startOffset="28" endLine="100" endOffset="70"/></Target><Target id="@+id/spinnerMonth" view="Spinner"><Expressions/><location startLine="117" startOffset="28" endLine="120" endOffset="70"/></Target><Target id="@+id/spinnerDay" view="Spinner"><Expressions/><location startLine="136" startOffset="28" endLine="139" endOffset="70"/></Target><Target id="@+id/spinnerHour" view="Spinner"><Expressions/><location startLine="170" startOffset="28" endLine="173" endOffset="70"/></Target><Target id="@+id/spinnerMinute" view="Spinner"><Expressions/><location startLine="189" startOffset="28" endLine="192" endOffset="70"/></Target><Target id="@+id/spinnerYearPillar" view="Spinner"><Expressions/><location startLine="204" startOffset="20" endLine="208" endOffset="59"/></Target><Target id="@+id/spinnerHourPillar" view="Spinner"><Expressions/><location startLine="218" startOffset="20" endLine="222" endOffset="59"/></Target><Target id="@+id/spinnerLocation" view="Spinner"><Expressions/><location startLine="232" startOffset="20" endLine="236" endOffset="59"/></Target><Target id="@+id/spinnerDisease" view="Spinner"><Expressions/><location startLine="246" startOffset="20" endLine="250" endOffset="60"/></Target><Target id="@+id/btnAdd" view="Button"><Expressions/><location startLine="259" startOffset="24" endLine="264" endOffset="49"/></Target><Target id="@+id/btnUpdate" view="Button"><Expressions/><location startLine="266" startOffset="24" endLine="272" endOffset="53"/></Target><Target id="@+id/btnDelete" view="Button"><Expressions/><location startLine="274" startOffset="24" endLine="280" endOffset="53"/></Target><Target id="@+id/btnStartSystem" view="Button"><Expressions/><location startLine="282" startOffset="24" endLine="286" endOffset="49"/></Target><Target id="@+id/rvPersons" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="316" startOffset="12" endLine="320" endOffset="43"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="323" startOffset="12" endLine="331" endOffset="39"/></Target></Targets></Layout>