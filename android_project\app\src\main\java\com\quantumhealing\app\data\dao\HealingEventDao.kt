package com.quantumhealing.app.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.quantumhealing.app.data.model.HealingEvent

/**
 * 疗愈事件数据访问对象
 */
@Dao
interface HealingEventDao {
    
    @Query("SELECT * FROM healing_events ORDER BY timestamp DESC LIMIT 100")
    fun getRecentEvents(): LiveData<List<HealingEvent>>
    
    @Query("SELECT * FROM healing_events WHERE personId = :personId ORDER BY timestamp DESC")
    fun getEventsByPersonId(personId: Long): LiveData<List<HealingEvent>>
    
    @Query("SELECT * FROM healing_events WHERE personName = :personName ORDER BY timestamp DESC")
    fun getEventsByPersonName(personName: String): LiveData<List<HealingEvent>>
    
    @Query("SELECT * FROM healing_events WHERE timestamp >= :startTime ORDER BY timestamp DESC")
    fun getEventsAfterTime(startTime: Long): LiveData<List<HealingEvent>>
    
    @Insert
    suspend fun insertEvent(event: HealingEvent): Long
    
    @Insert
    suspend fun insertEvents(events: List<HealingEvent>)
    
    @Delete
    suspend fun deleteEvent(event: HealingEvent)
    
    @Query("DELETE FROM healing_events WHERE personId = :personId")
    suspend fun deleteEventsByPersonId(personId: Long)
    
    @Query("DELETE FROM healing_events WHERE timestamp < :cutoffTime")
    suspend fun deleteOldEvents(cutoffTime: Long)
    
    @Query("SELECT COUNT(*) FROM healing_events")
    suspend fun getEventCount(): Int
    
    @Query("SELECT COUNT(*) FROM healing_events WHERE personId = :personId")
    suspend fun getEventCountByPersonId(personId: Long): Int
    
    @Query("SELECT * FROM healing_events WHERE personId IN (SELECT id FROM persons WHERE isActive = 1) ORDER BY timestamp DESC LIMIT 50")
    fun getLatestActiveEvents(): LiveData<List<HealingEvent>>
}
