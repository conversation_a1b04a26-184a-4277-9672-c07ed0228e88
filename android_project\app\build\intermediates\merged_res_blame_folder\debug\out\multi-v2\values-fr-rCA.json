{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-35:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,320,420,542,627,693,790,870,932,1024,1098,1159,1238,1302,1356,1472,1531,1593,1647,1729,1858,1950,2034,2148,2227,2308,2401,2468,2534,2613,2694,2785,2857,2935,3010,3082,3179,3256,3354,3452,3530,3611,3711,3768,3834,3917,4004,4066,4130,4193,4295,4402,4499,4608", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,99,121,84,65,96,79,61,91,73,60,78,63,53,115,58,61,53,81,128,91,83,113,78,80,92,66,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,88", "endOffsets": "233,315,415,537,622,688,785,865,927,1019,1093,1154,1233,1297,1351,1467,1526,1588,1642,1724,1853,1945,2029,2143,2222,2303,2396,2463,2529,2608,2689,2780,2852,2930,3005,3077,3174,3251,3349,3447,3525,3606,3706,3763,3829,3912,3999,4061,4125,4188,4290,4397,4494,4603,4692"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3038,3120,3220,3342,3427,3493,3590,3670,3732,3824,3898,3959,4038,4102,4156,4272,4331,4393,4447,4529,4658,4750,4834,4948,5027,5108,5201,5268,5334,5413,5494,5585,5657,5735,5810,5882,5979,6056,6154,6252,6330,6411,6511,6568,6634,6717,6804,6866,6930,6993,7095,7202,7299,7408", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,81,99,121,84,65,96,79,61,91,73,60,78,63,53,115,58,61,53,81,128,91,83,113,78,80,92,66,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,88", "endOffsets": "283,3115,3215,3337,3422,3488,3585,3665,3727,3819,3893,3954,4033,4097,4151,4267,4326,4388,4442,4524,4653,4745,4829,4943,5022,5103,5196,5263,5329,5408,5489,5580,5652,5730,5805,5877,5974,6051,6149,6247,6325,6406,6506,6563,6629,6712,6799,6861,6925,6988,7090,7197,7294,7403,7492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bea0caf4c4157f01bbd28088de62123\\transformed\\appcompat-1.5.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "288,399,506,616,703,809,939,1024,1104,1195,1288,1386,1481,1581,1674,1767,1862,1953,2044,2130,2240,2351,2454,2565,2673,2780,2939,7497", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "394,501,611,698,804,934,1019,1099,1190,1283,1381,1476,1576,1669,1762,1857,1948,2039,2125,2235,2346,2449,2560,2668,2775,2934,3033,7579"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d0cd04beb3e7b9a1429c24c8d7d0af4\\transformed\\core-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7584", "endColumns": "100", "endOffsets": "7680"}}]}]}