# Android SDK 问题解决指南

## 🚨 当前问题
```
Failed to find Platform SDK with path: platforms;android-33
```

## 🔧 解决方案

### 方案一：安装所需的 SDK（推荐）

1. **打开 Android Studio**
2. **进入 SDK Manager**：
   - 点击 `File` → `Settings` (Windows) 或 `Android Studio` → `Preferences` (Mac)
   - 选择 `Appearance & Behavior` → `System Settings` → `Android SDK`
   - 或者直接点击工具栏的 SDK Manager 图标

3. **安装 Android 11 (API 30)**：
   - 在 `SDK Platforms` 标签页
   - 勾选 `Android 11.0 (R)` - API Level 30
   - 点击 `Apply` 开始下载

4. **安装 Build Tools**：
   - 切换到 `SDK Tools` 标签页
   - 确保 `Android SDK Build-Tools 30.0.3` 已安装

### 方案二：检查已有 SDK 版本

运行检查脚本：
```bash
# Windows
double-click check_sdk.bat

# 或在命令行中
cd android_project
check_sdk.bat
```

### 方案三：手动配置 SDK 路径

1. **找到您的 Android SDK 路径**：
   - 通常在：`C:\Users\<USER>\AppData\Local\Android\Sdk`
   - 或：`%LOCALAPPDATA%\Android\Sdk`

2. **创建 local.properties 文件**：
   ```
   sdk.dir=C\:\\Users\\YourName\\AppData\\Local\\Android\\Sdk
   ```

3. **检查 platforms 文件夹**：
   - 进入 SDK 目录的 `platforms` 文件夹
   - 查看有哪些 `android-XX` 文件夹
   - 记下最高的版本号

## 📱 已调整的项目配置

我已经将项目降级到 **API 30**，这是最常见的版本：

```gradle
android {
    compileSdk 30
    defaultConfig {
        targetSdk 30
        // ...
    }
}
```

## 🔍 验证步骤

1. **检查 SDK 安装**：
   - Android Studio → SDK Manager
   - 确认 Android 11.0 (API 30) 已安装

2. **同步项目**：
   - 点击 `Sync Now` 或 `File` → `Sync Project with Gradle Files`

3. **清理重建**：
   - `Build` → `Clean Project`
   - `Build` → `Rebuild Project`

## 🆘 如果仍有问题

### 检查清单：
- [ ] Android Studio 版本是否为 2022.1.1 或更新
- [ ] JDK 版本是否为 11 或更高
- [ ] 网络连接是否正常（下载 SDK 需要）
- [ ] 磁盘空间是否充足（至少 2GB）

### 常见错误及解决：

**错误**: `SDK location not found`
**解决**: 创建 `local.properties` 文件并设置正确的 SDK 路径

**错误**: `Build Tools version not found`
**解决**: 在 SDK Manager 中安装对应版本的 Build Tools

**错误**: `Gradle sync failed`
**解决**: 检查网络连接，清理 Gradle 缓存

## 📞 获取帮助

如果上述方法都不行，请告诉我：
1. 您的 Android Studio 版本
2. 运行 `check_sdk.bat` 的结果
3. `local.properties` 文件的内容
4. 完整的错误信息

我会根据您的具体情况提供定制化解决方案。

---

**目标**: 让您的量子疗愈系统成功编译运行！ ✨
