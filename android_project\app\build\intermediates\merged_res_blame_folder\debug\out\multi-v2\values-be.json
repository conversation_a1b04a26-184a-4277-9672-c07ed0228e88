{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-35:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d0cd04beb3e7b9a1429c24c8d7d0af4\\transformed\\core-1.8.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "90", "startColumns": "4", "startOffsets": "7631", "endColumns": "100", "endOffsets": "7727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bea0caf4c4157f01bbd28088de62123\\transformed\\appcompat-1.5.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "387,507,610,726,812,917,1036,1116,1193,1285,1379,1474,1568,1663,1757,1853,1948,2040,2132,2213,2319,2424,2522,2630,2736,2844,3017,7549", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "502,605,721,807,912,1031,1111,1188,1280,1374,1469,1563,1658,1752,1848,1943,2035,2127,2208,2314,2419,2517,2625,2731,2839,3012,3112,7626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,337,419,516,632,715,782,875,952,1015,1131,1200,1259,1330,1389,1443,1564,1625,1688,1742,1815,1937,2025,2108,2230,2316,2403,2494,2561,2627,2699,2776,2860,2935,3012,3094,3170,3259,3341,3432,3528,3602,3683,3778,3832,3898,3985,4071,4133,4197,4260,4370,4477,4580,4689", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,81,96,115,82,66,92,76,62,115,68,58,70,58,53,120,60,62,53,72,121,87,82,121,85,86,90,66,65,71,76,83,74,76,81,75,88,81,90,95,73,80,94,53,65,86,85,61,63,62,109,106,102,108,79", "endOffsets": "332,414,511,627,710,777,870,947,1010,1126,1195,1254,1325,1384,1438,1559,1620,1683,1737,1810,1932,2020,2103,2225,2311,2398,2489,2556,2622,2694,2771,2855,2930,3007,3089,3165,3254,3336,3427,3523,3597,3678,3773,3827,3893,3980,4066,4128,4192,4255,4365,4472,4575,4684,4764"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3117,3199,3296,3412,3495,3562,3655,3732,3795,3911,3980,4039,4110,4169,4223,4344,4405,4468,4522,4595,4717,4805,4888,5010,5096,5183,5274,5341,5407,5479,5556,5640,5715,5792,5874,5950,6039,6121,6212,6308,6382,6463,6558,6612,6678,6765,6851,6913,6977,7040,7150,7257,7360,7469", "endLines": "7,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "endColumns": "12,81,96,115,82,66,92,76,62,115,68,58,70,58,53,120,60,62,53,72,121,87,82,121,85,86,90,66,65,71,76,83,74,76,81,75,88,81,90,95,73,80,94,53,65,86,85,61,63,62,109,106,102,108,79", "endOffsets": "382,3194,3291,3407,3490,3557,3650,3727,3790,3906,3975,4034,4105,4164,4218,4339,4400,4463,4517,4590,4712,4800,4883,5005,5091,5178,5269,5336,5402,5474,5551,5635,5710,5787,5869,5945,6034,6116,6207,6303,6377,6458,6553,6607,6673,6760,6846,6908,6972,7035,7145,7252,7355,7464,7544"}}]}]}