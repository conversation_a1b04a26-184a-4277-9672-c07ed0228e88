{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-35:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,310,408,523,606,673,772,840,901,989,1055,1119,1190,1253,1307,1416,1475,1538,1592,1666,1791,1881,1961,2076,2159,2241,2332,2399,2465,2536,2616,2702,2780,2858,2931,3006,3093,3180,3271,3364,3436,3512,3604,3655,3721,3805,3891,3953,4017,4080,4187,4292,4388,4494", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,97,114,82,66,98,67,60,87,65,63,70,62,53,108,58,62,53,73,124,89,79,114,82,81,90,66,65,70,79,85,77,77,72,74,86,86,90,92,71,75,91,50,65,83,85,61,63,62,106,104,95,105,82", "endOffsets": "225,305,403,518,601,668,767,835,896,984,1050,1114,1185,1248,1302,1411,1470,1533,1587,1661,1786,1876,1956,2071,2154,2236,2327,2394,2460,2531,2611,2697,2775,2853,2926,3001,3088,3175,3266,3359,3431,3507,3599,3650,3716,3800,3886,3948,4012,4075,4182,4287,4383,4489,4572"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3025,3105,3203,3318,3401,3468,3567,3635,3696,3784,3850,3914,3985,4048,4102,4211,4270,4333,4387,4461,4586,4676,4756,4871,4954,5036,5127,5194,5260,5331,5411,5497,5575,5653,5726,5801,5888,5975,6066,6159,6231,6307,6399,6450,6516,6600,6686,6748,6812,6875,6982,7087,7183,7289", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,79,97,114,82,66,98,67,60,87,65,63,70,62,53,108,58,62,53,73,124,89,79,114,82,81,90,66,65,70,79,85,77,77,72,74,86,86,90,92,71,75,91,50,65,83,85,61,63,62,106,104,95,105,82", "endOffsets": "275,3100,3198,3313,3396,3463,3562,3630,3691,3779,3845,3909,3980,4043,4097,4206,4265,4328,4382,4456,4581,4671,4751,4866,4949,5031,5122,5189,5255,5326,5406,5492,5570,5648,5721,5796,5883,5970,6061,6154,6226,6302,6394,6445,6511,6595,6681,6743,6807,6870,6977,7082,7178,7284,7367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bea0caf4c4157f01bbd28088de62123\\transformed\\appcompat-1.5.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "280,389,487,597,683,789,913,999,1080,1172,1266,1362,1456,1557,1651,1747,1844,1936,2029,2111,2220,2329,2428,2537,2644,2755,2926,7372", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "384,482,592,678,784,908,994,1075,1167,1261,1357,1451,1552,1646,1742,1839,1931,2024,2106,2215,2324,2423,2532,2639,2750,2921,3020,7450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d0cd04beb3e7b9a1429c24c8d7d0af4\\transformed\\core-1.8.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7455", "endColumns": "100", "endOffsets": "7551"}}]}]}