{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-35:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bea0caf4c4157f01bbd28088de62123\\transformed\\appcompat-1.5.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "273,380,484,591,678,778,898,976,1053,1144,1237,1332,1426,1526,1619,1714,1808,1899,1990,2070,2176,2277,2374,2483,2583,2693,2853,7274", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "375,479,586,673,773,893,971,1048,1139,1232,1327,1421,1521,1614,1709,1803,1894,1985,2065,2171,2272,2369,2478,2578,2688,2848,2951,7350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,305,404,524,608,671,762,829,888,978,1043,1107,1176,1238,1292,1407,1465,1526,1580,1653,1780,1866,1950,2053,2128,2204,2290,2357,2423,2496,2576,2661,2732,2808,2887,2956,3052,3130,3225,3321,3395,3470,3569,3620,3687,3774,3864,3926,3990,4053,4155,4260,4357,4463", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,98,119,83,62,90,66,58,89,64,63,68,61,53,114,57,60,53,72,126,85,83,102,74,75,85,66,65,72,79,84,70,75,78,68,95,77,94,95,73,74,98,50,66,86,89,61,63,62,101,104,96,105,77", "endOffsets": "218,300,399,519,603,666,757,824,883,973,1038,1102,1171,1233,1287,1402,1460,1521,1575,1648,1775,1861,1945,2048,2123,2199,2285,2352,2418,2491,2571,2656,2727,2803,2882,2951,3047,3125,3220,3316,3390,3465,3564,3615,3682,3769,3859,3921,3985,4048,4150,4255,4352,4458,4536"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2956,3038,3137,3257,3341,3404,3495,3562,3621,3711,3776,3840,3909,3971,4025,4140,4198,4259,4313,4386,4513,4599,4683,4786,4861,4937,5023,5090,5156,5229,5309,5394,5465,5541,5620,5689,5785,5863,5958,6054,6128,6203,6302,6353,6420,6507,6597,6659,6723,6786,6888,6993,7090,7196", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,81,98,119,83,62,90,66,58,89,64,63,68,61,53,114,57,60,53,72,126,85,83,102,74,75,85,66,65,72,79,84,70,75,78,68,95,77,94,95,73,74,98,50,66,86,89,61,63,62,101,104,96,105,77", "endOffsets": "268,3033,3132,3252,3336,3399,3490,3557,3616,3706,3771,3835,3904,3966,4020,4135,4193,4254,4308,4381,4508,4594,4678,4781,4856,4932,5018,5085,5151,5224,5304,5389,5460,5536,5615,5684,5780,5858,5953,6049,6123,6198,6297,6348,6415,6502,6592,6654,6718,6781,6883,6988,7085,7191,7269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d0cd04beb3e7b9a1429c24c8d7d0af4\\transformed\\core-1.8.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7355", "endColumns": "100", "endOffsets": "7451"}}]}]}