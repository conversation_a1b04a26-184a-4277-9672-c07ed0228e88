<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_person" modulePackage="com.quantumhealing.app" filePath="app\src\main\res\layout\item_person.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_person_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="80" endOffset="51"/></Target><Target id="@+id/vStatusIndicator" view="View"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="70"/></Target><Target id="@+id/tvName" view="TextView"><Expressions/><location startLine="34" startOffset="16" endLine="41" endOffset="46"/></Target><Target id="@+id/tvGender" view="TextView"><Expressions/><location startLine="43" startOffset="16" endLine="49" endOffset="54"/></Target><Target id="@+id/tvDisease" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="59" endOffset="48"/></Target><Target id="@+id/tvBirthInfo" view="TextView"><Expressions/><location startLine="61" startOffset="12" endLine="68" endOffset="48"/></Target><Target id="@+id/swActive" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="72" startOffset="8" endLine="77" endOffset="46"/></Target></Targets></Layout>