-- Merging decision tree log ---
manifest
ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:2:1-42:12
INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:2:1-42:12
INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:2:1-42:12
INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:2:1-42:12
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3e0850c68046896e81e6bc90bcdb463\transformed\viewbinding-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\be7e8d160a7987a5c1c6e0a0a9f25765\transformed\lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-ktx:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f992dc85e02e5be8ac559259b2fd1783\transformed\room-ktx-2.4.3\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4e112358a03acfa0a8eb6e946ce96973\transformed\material-1.6.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\8177fc0ae5949b09fa6ab8b305676505\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\640105fec6e442a9bfcfa36420e04996\transformed\appcompat-resources-1.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bea0caf4c4157f01bbd28088de62123\transformed\appcompat-1.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e854dc173145741c17288d2b8e5ed608\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\0814a142ad1dd5b790d941dd8dce2a92\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a71dcecfb9639189bd48003b8e479ee3\transformed\activity-1.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\078ab102ad2f0d28c3fa3e33c18819c6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f4efd14d87c979a750a0e7d631c7215\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\06840955b76f998776603c8e6e239b16\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b262174259bdb9d68d565f8dd6956f8\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\00760b6e43bf6f24a6283f78e3e474e0\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\a75c68e6d7b65f538621d2391687e2c5\transformed\lifecycle-livedata-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f2e38536a758825fd1f1c5c3f7b0480\transformed\core-ktx-1.8.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.lifecycle:lifecycle-service:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\424137b3a436567c8643639a7f8d7d8d\transformed\lifecycle-service-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\773c56230032821b5a829b1fd0a22389\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe7d5922fdbcbc9c6cb2470ce49bf904\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\14fb7321df2c8efd84e881f44ffe8981\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb0d6b6e1094c723ecdc033cf5f1650b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2dc903d36dff689c28920e041dbe2b4\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3131f2a29cd97810f7003d6130d3fec8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20a3fb5df4904e943d3194ad8a03f3b6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad05cc9213b4d5a7c1c99e670e93e3f9\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\741a16d27febc46cfd6b09efd0f8ad1c\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d0cd04beb3e7b9a1429c24c8d7d0af4\transformed\core-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5da1c4ec72ae4d041bbb1baebb9f20c5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5938433006c76a70637123477baffd0f\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\0cbabd7580c607f0847b9ecf0a703431\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f0a8b431b03188424de101bf61e8e85\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\33259d75f8873497285bc72e5617aaa5\transformed\sqlite-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e31212a00fd25c60458e58354adc6d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\04de2eb05ea6d538f4c2d348c2349aa7\transformed\savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\71ab9a8073c0ca5b314a5a684d37123f\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\ea0a158a81b0ae0d944bce7a1af88146\transformed\lifecycle-livedata-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\bddd58e5a9354b2bf8c72a414a60899a\transformed\lifecycle-livedata-core-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ab50bb287cdbd6637961a1dfa0c816f\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bbff0d56b7a94db51087eabc2395eda1\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0be700c9754429f0e9d143616dc00db2\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b0fa39cdab60346c5d4f75d6a2b74611\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9632c531ef9e8b9b93d38c3393006279\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\041f4add30e80fb020c80f1480edcdce\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5229251263650b0fd5e3e189c71ac56e\transformed\annotation-experimental-1.1.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:6:5-77
	android:name
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:6:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:7:5-68
	android:name
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:7:22-65
uses-permission#android.permission.VIBRATE
ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:8:5-66
	android:name
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:8:22-63
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:9:5-77
	android:name
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:9:22-74
application
ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:11:5-40:19
INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:11:5-40:19
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4e112358a03acfa0a8eb6e946ce96973\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4e112358a03acfa0a8eb6e946ce96973\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\8177fc0ae5949b09fa6ab8b305676505\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\8177fc0ae5949b09fa6ab8b305676505\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d0cd04beb3e7b9a1429c24c8d7d0af4\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d0cd04beb3e7b9a1429c24c8d7d0af4\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e31212a00fd25c60458e58354adc6d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e31212a00fd25c60458e58354adc6d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d0cd04beb3e7b9a1429c24c8d7d0af4\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:18:9-35
	android:label
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:16:9-41
	android:fullBackupContent
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:14:9-54
	android:roundIcon
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:17:9-54
	tools:targetApi
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:20:9-29
	android:icon
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:15:9-43
	android:allowBackup
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:12:9-35
	android:theme
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:19:9-52
	android:dataExtractionRules
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:13:9-65
activity#com.quantumhealing.app.MainActivity
ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:23:9-31:20
	android:exported
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:26:13-56
	android:name
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:24:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:27:13-30:29
action#android.intent.action.MAIN
ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:28:17-69
	android:name
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:28:25-66
category#android.intent.category.LAUNCHER
ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:29:17-77
	android:name
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:29:27-74
service#com.quantumhealing.app.service.QuantumHealingService
ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:34:9-38:58
	android:enabled
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:36:13-35
	android:exported
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:37:13-37
	android:foregroundServiceType
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:38:13-55
	android:name
		ADDED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:35:13-58
uses-sdk
INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml
INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3e0850c68046896e81e6bc90bcdb463\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3e0850c68046896e81e6bc90bcdb463\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\be7e8d160a7987a5c1c6e0a0a9f25765\transformed\lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\be7e8d160a7987a5c1c6e0a0a9f25765\transformed\lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-ktx:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f992dc85e02e5be8ac559259b2fd1783\transformed\room-ktx-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-ktx:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\f992dc85e02e5be8ac559259b2fd1783\transformed\room-ktx-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4e112358a03acfa0a8eb6e946ce96973\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4e112358a03acfa0a8eb6e946ce96973\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\8177fc0ae5949b09fa6ab8b305676505\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\8177fc0ae5949b09fa6ab8b305676505\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\640105fec6e442a9bfcfa36420e04996\transformed\appcompat-resources-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\640105fec6e442a9bfcfa36420e04996\transformed\appcompat-resources-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bea0caf4c4157f01bbd28088de62123\transformed\appcompat-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bea0caf4c4157f01bbd28088de62123\transformed\appcompat-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e854dc173145741c17288d2b8e5ed608\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e854dc173145741c17288d2b8e5ed608\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\0814a142ad1dd5b790d941dd8dce2a92\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\0814a142ad1dd5b790d941dd8dce2a92\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a71dcecfb9639189bd48003b8e479ee3\transformed\activity-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a71dcecfb9639189bd48003b8e479ee3\transformed\activity-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\078ab102ad2f0d28c3fa3e33c18819c6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\078ab102ad2f0d28c3fa3e33c18819c6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f4efd14d87c979a750a0e7d631c7215\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f4efd14d87c979a750a0e7d631c7215\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\06840955b76f998776603c8e6e239b16\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\06840955b76f998776603c8e6e239b16\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b262174259bdb9d68d565f8dd6956f8\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b262174259bdb9d68d565f8dd6956f8\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\00760b6e43bf6f24a6283f78e3e474e0\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\00760b6e43bf6f24a6283f78e3e474e0\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\a75c68e6d7b65f538621d2391687e2c5\transformed\lifecycle-livedata-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\a75c68e6d7b65f538621d2391687e2c5\transformed\lifecycle-livedata-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f2e38536a758825fd1f1c5c3f7b0480\transformed\core-ktx-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f2e38536a758825fd1f1c5c3f7b0480\transformed\core-ktx-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.lifecycle:lifecycle-service:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\424137b3a436567c8643639a7f8d7d8d\transformed\lifecycle-service-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-service:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\424137b3a436567c8643639a7f8d7d8d\transformed\lifecycle-service-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\773c56230032821b5a829b1fd0a22389\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\773c56230032821b5a829b1fd0a22389\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe7d5922fdbcbc9c6cb2470ce49bf904\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe7d5922fdbcbc9c6cb2470ce49bf904\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\14fb7321df2c8efd84e881f44ffe8981\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\14fb7321df2c8efd84e881f44ffe8981\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb0d6b6e1094c723ecdc033cf5f1650b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb0d6b6e1094c723ecdc033cf5f1650b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2dc903d36dff689c28920e041dbe2b4\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2dc903d36dff689c28920e041dbe2b4\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3131f2a29cd97810f7003d6130d3fec8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3131f2a29cd97810f7003d6130d3fec8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20a3fb5df4904e943d3194ad8a03f3b6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20a3fb5df4904e943d3194ad8a03f3b6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad05cc9213b4d5a7c1c99e670e93e3f9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad05cc9213b4d5a7c1c99e670e93e3f9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\741a16d27febc46cfd6b09efd0f8ad1c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\741a16d27febc46cfd6b09efd0f8ad1c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d0cd04beb3e7b9a1429c24c8d7d0af4\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d0cd04beb3e7b9a1429c24c8d7d0af4\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5da1c4ec72ae4d041bbb1baebb9f20c5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5da1c4ec72ae4d041bbb1baebb9f20c5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5938433006c76a70637123477baffd0f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5938433006c76a70637123477baffd0f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\0cbabd7580c607f0847b9ecf0a703431\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\0cbabd7580c607f0847b9ecf0a703431\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f0a8b431b03188424de101bf61e8e85\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f0a8b431b03188424de101bf61e8e85\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\33259d75f8873497285bc72e5617aaa5\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\33259d75f8873497285bc72e5617aaa5\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e31212a00fd25c60458e58354adc6d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e31212a00fd25c60458e58354adc6d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\04de2eb05ea6d538f4c2d348c2349aa7\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\04de2eb05ea6d538f4c2d348c2349aa7\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\71ab9a8073c0ca5b314a5a684d37123f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\71ab9a8073c0ca5b314a5a684d37123f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\ea0a158a81b0ae0d944bce7a1af88146\transformed\lifecycle-livedata-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\ea0a158a81b0ae0d944bce7a1af88146\transformed\lifecycle-livedata-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\bddd58e5a9354b2bf8c72a414a60899a\transformed\lifecycle-livedata-core-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\bddd58e5a9354b2bf8c72a414a60899a\transformed\lifecycle-livedata-core-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ab50bb287cdbd6637961a1dfa0c816f\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ab50bb287cdbd6637961a1dfa0c816f\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bbff0d56b7a94db51087eabc2395eda1\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bbff0d56b7a94db51087eabc2395eda1\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0be700c9754429f0e9d143616dc00db2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0be700c9754429f0e9d143616dc00db2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b0fa39cdab60346c5d4f75d6a2b74611\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b0fa39cdab60346c5d4f75d6a2b74611\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9632c531ef9e8b9b93d38c3393006279\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9632c531ef9e8b9b93d38c3393006279\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\041f4add30e80fb020c80f1480edcdce\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\041f4add30e80fb020c80f1480edcdce\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5229251263650b0fd5e3e189c71ac56e\transformed\annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5229251263650b0fd5e3e189c71ac56e\transformed\annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:26:13-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d71459ff4b6a1fa83de23a6a694dc65\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
