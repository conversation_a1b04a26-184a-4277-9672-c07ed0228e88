{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-33:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,240,316,422,551,636,701,791,866,925,1016,1081,1140,1211,1273,1330,1449,1507,1568,1623,1696,1828,1919,2008,2119,2197,2274,2366,2433,2499,2571,2653,2735,2810,2884,2956,3035,3132,3213,3299,3391,3465,3544,3630,3684,3752,3835,3916,3978,4042,4105,4217,4320,4424,4529", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,75,105,128,84,64,89,74,58,90,64,58,70,61,56,118,57,60,54,72,131,90,88,110,77,76,91,66,65,71,81,81,74,73,71,78,96,80,85,91,73,78,85,53,67,82,80,61,63,62,111,102,103,104,81", "endOffsets": "235,311,417,546,631,696,786,861,920,1011,1076,1135,1206,1268,1325,1444,1502,1563,1618,1691,1823,1914,2003,2114,2192,2269,2361,2428,2494,2566,2648,2730,2805,2879,2951,3030,3127,3208,3294,3386,3460,3539,3625,3679,3747,3830,3911,3973,4037,4100,4212,4315,4419,4524,4606"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3051,3127,3233,3362,3447,3512,3602,3677,3736,3827,3892,3951,4022,4084,4141,4260,4318,4379,4434,4507,4639,4730,4819,4930,5008,5085,5177,5244,5310,5382,5464,5546,5621,5695,5767,5846,5943,6024,6110,6202,6276,6355,6441,6495,6563,6646,6727,6789,6853,6916,7028,7131,7235,7340", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,75,105,128,84,64,89,74,58,90,64,58,70,61,56,118,57,60,54,72,131,90,88,110,77,76,91,66,65,71,81,81,74,73,71,78,96,80,85,91,73,78,85,53,67,82,80,61,63,62,111,102,103,104,81", "endOffsets": "285,3122,3228,3357,3442,3507,3597,3672,3731,3822,3887,3946,4017,4079,4136,4255,4313,4374,4429,4502,4634,4725,4814,4925,5003,5080,5172,5239,5305,5377,5459,5541,5616,5690,5762,5841,5938,6019,6105,6197,6271,6350,6436,6490,6558,6641,6722,6784,6848,6911,7023,7126,7230,7335,7417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d780b2c0a0ca762ae8fc4516db86618\\transformed\\appcompat-1.4.2\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "290,403,510,626,713,822,945,1024,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2124,2239,2348,2447,2573,2680,2788,2948,7422", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "398,505,621,708,817,940,1019,1097,1188,1281,1376,1470,1570,1663,1758,1852,1943,2034,2119,2234,2343,2442,2568,2675,2783,2943,3046,7503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c33bda6cfdbe9dfccb7561f3cbbee362\\transformed\\core-1.7.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7508", "endColumns": "100", "endOffsets": "7604"}}]}]}