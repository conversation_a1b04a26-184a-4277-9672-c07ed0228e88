{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-33:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c33bda6cfdbe9dfccb7561f3cbbee362\\transformed\\core-1.7.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7634", "endColumns": "100", "endOffsets": "7730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d780b2c0a0ca762ae8fc4516db86618\\transformed\\appcompat-1.4.2\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "280,384,488,596,681,782,910,996,1077,1169,1263,1360,1454,1554,1648,1744,1839,1931,2023,2104,2212,2319,2426,2535,2640,2754,2931,7551", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "379,483,591,676,777,905,991,1072,1164,1258,1355,1449,1549,1643,1739,1834,1926,2018,2099,2207,2314,2421,2530,2635,2749,2926,3025,7629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,315,420,553,633,711,807,886,949,1044,1113,1176,1250,1314,1370,1491,1549,1611,1667,1744,1883,1971,2051,2161,2241,2321,2411,2478,2544,2623,2704,2792,2871,2948,3030,3119,3203,3295,3388,3489,3563,3655,3757,3809,3875,3967,4055,4117,4181,4244,4355,4457,4563,4666", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,104,132,79,77,95,78,62,94,68,62,73,63,55,120,57,61,55,76,138,87,79,109,79,79,89,66,65,78,80,87,78,76,81,88,83,91,92,100,73,91,101,51,65,91,87,61,63,62,110,101,105,102,84", "endOffsets": "225,310,415,548,628,706,802,881,944,1039,1108,1171,1245,1309,1365,1486,1544,1606,1662,1739,1878,1966,2046,2156,2236,2316,2406,2473,2539,2618,2699,2787,2866,2943,3025,3114,3198,3290,3383,3484,3558,3650,3752,3804,3870,3962,4050,4112,4176,4239,4350,4452,4558,4661,4746"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3030,3115,3220,3353,3433,3511,3607,3686,3749,3844,3913,3976,4050,4114,4170,4291,4349,4411,4467,4544,4683,4771,4851,4961,5041,5121,5211,5278,5344,5423,5504,5592,5671,5748,5830,5919,6003,6095,6188,6289,6363,6455,6557,6609,6675,6767,6855,6917,6981,7044,7155,7257,7363,7466", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,84,104,132,79,77,95,78,62,94,68,62,73,63,55,120,57,61,55,76,138,87,79,109,79,79,89,66,65,78,80,87,78,76,81,88,83,91,92,100,73,91,101,51,65,91,87,61,63,62,110,101,105,102,84", "endOffsets": "275,3110,3215,3348,3428,3506,3602,3681,3744,3839,3908,3971,4045,4109,4165,4286,4344,4406,4462,4539,4678,4766,4846,4956,5036,5116,5206,5273,5339,5418,5499,5587,5666,5743,5825,5914,5998,6090,6183,6284,6358,6450,6552,6604,6670,6762,6850,6912,6976,7039,7150,7252,7358,7461,7546"}}]}]}