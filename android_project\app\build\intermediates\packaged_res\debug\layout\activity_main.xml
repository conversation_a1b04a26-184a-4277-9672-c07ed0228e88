<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- 左侧输入面板 -->
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginEnd="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="人员信息录入"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <!-- 姓名输入 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="姓名">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 性别选择 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="性别"
                        android:textSize="14sp"
                        android:layout_marginBottom="4dp" />

                    <Spinner
                        android:id="@+id/spinnerGender"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp" />

                    <!-- 公历生日 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="公历生日"
                        android:textSize="14sp"
                        android:layout_marginBottom="4dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="4dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="年"
                                android:textSize="12sp" />

                            <Spinner
                                android:id="@+id/spinnerYear"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="4dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="月"
                                android:textSize="12sp" />

                            <Spinner
                                android:id="@+id/spinnerMonth"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="4dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="日"
                                android:textSize="12sp" />

                            <Spinner
                                android:id="@+id/spinnerDay"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- 出生时间 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="出生时间"
                        android:textSize="14sp"
                        android:layout_marginBottom="4dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="4dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="时"
                                android:textSize="12sp" />

                            <Spinner
                                android:id="@+id/spinnerHour"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="4dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="分"
                                android:textSize="12sp" />

                            <Spinner
                                android:id="@+id/spinnerMinute"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- 年柱选择 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="年柱"
                        android:textSize="14sp"
                        android:layout_marginBottom="4dp" />

                    <Spinner
                        android:id="@+id/spinnerYearPillar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp" />

                    <!-- 时柱选择 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="时柱"
                        android:textSize="14sp"
                        android:layout_marginBottom="4dp" />

                    <Spinner
                        android:id="@+id/spinnerHourPillar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp" />

                    <!-- 出生地选择 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="出生地"
                        android:textSize="14sp"
                        android:layout_marginBottom="4dp" />

                    <Spinner
                        android:id="@+id/spinnerLocation"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp" />

                    <!-- 疾病选择 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="疾病"
                        android:textSize="14sp"
                        android:layout_marginBottom="4dp" />

                    <Spinner
                        android:id="@+id/spinnerDisease"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp" />

                    <!-- 按钮区域 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <Button
                            android:id="@+id/btnAdd"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:text="添加人员" />

                        <Button
                            android:id="@+id/btnUpdate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:text="更新人员"
                            android:enabled="false" />

                        <Button
                            android:id="@+id/btnDelete"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:text="删除人员"
                            android:enabled="false" />

                        <Button
                            android:id="@+id/btnStartSystem"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="启动系统" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </ScrollView>

    <!-- 右侧人员列表 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginStart="8dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="人员列表"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvPersons"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <!-- 状态栏 -->
            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="就绪 | 当前人员: 0"
                android:textSize="12sp"
                android:background="@color/design_default_color_surface"
                android:padding="8dp" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
