package com.quantumhealing.app

import com.quantumhealing.app.data.Constants
import com.quantumhealing.app.data.LocationMapping
import com.quantumhealing.app.data.model.HealingEvent
import com.quantumhealing.app.data.model.Person
import org.junit.Test
import org.junit.Assert.*

/**
 * 量子疗愈系统核心功能测试
 */
class QuantumHealingTest {

    @Test
    fun testPersonBioSignatureGeneration() {
        val person = Person(
            name = "测试用户",
            gender = "男",
            gregorianBirthday = "1990-01-01",
            approxBirthTime = "12:00",
            yearPillar = "庚午",
            hourPillar = "壬午",
            birthStargateCoord = "(116.40, 39.90)",
            disease = "通用"
        )

        val bioSignature = person.generateBioSignature()
        assertNotNull(bioSignature)
        assertTrue(bioSignature.contains("庚午:壬午@"))
        assertTrue(bioSignature.endsWith("Ψ"))
    }

    @Test
    fun testHealingEventCreation() {
        val event = HealingEvent.create(
            personId = 1L,
            personName = "测试用户",
            frequency = "7.83Hz"
        )

        assertNotNull(event)
        assertEquals("测试用户", event.personName)
        assertEquals("7.83Hz", event.frequency)
        assertTrue(event.energyLevel in 80..100)
        assertTrue(event.wave.startsWith("M42_7.83Se_"))
        assertTrue(event.phase.contains("π/"))
        assertTrue(event.phase.contains("λ"))
    }

    @Test
    fun testHealingFrequencies() {
        // 测试通用频率
        val generalFreqs = Constants.HEALING_FREQUENCIES["通用"]
        assertNotNull(generalFreqs)
        assertEquals(3, generalFreqs?.size)
        assertTrue(generalFreqs?.contains("7.83Hz") == true)

        // 测试癌症频率
        val cancerFreqs = Constants.HEALING_FREQUENCIES["癌症（恶性肿瘤）"]
        assertNotNull(cancerFreqs)
        assertTrue(cancerFreqs?.contains("152Hz") == true)

        // 测试所有疾病都有对应频率
        Constants.DISEASES.forEach { disease ->
            val freqs = Constants.HEALING_FREQUENCIES[disease]
            assertNotNull("疾病 $disease 缺少频率配置", freqs)
            assertTrue("疾病 $disease 频率列表为空", freqs?.isNotEmpty() == true)
        }
    }

    @Test
    fun testLocationMapping() {
        // 测试北京坐标
        val beijingCoord = LocationMapping.LOCATION_MAPPING["北京"]
        assertEquals("(116.40, 39.90)", beijingCoord)

        // 测试上海坐标
        val shanghaiCoord = LocationMapping.LOCATION_MAPPING["上海"]
        assertEquals("(121.47, 31.23)", shanghaiCoord)

        // 测试所有地区都有坐标
        LocationMapping.LOCATIONS.forEach { location ->
            val coord = LocationMapping.LOCATION_MAPPING[location]
            assertNotNull("地区 $location 缺少坐标", coord)
            assertTrue("地区 $location 坐标格式错误", coord?.matches(Regex("\\([^)]+\\)")) == true)
        }
    }

    @Test
    fun testYearAndHourPillars() {
        // 测试年柱数量
        assertEquals(60, Constants.YEAR_PILLARS.size)
        assertTrue(Constants.YEAR_PILLARS.contains("甲子"))
        assertTrue(Constants.YEAR_PILLARS.contains("癸亥"))

        // 测试时柱数量
        assertEquals(60, Constants.HOUR_PILLARS.size)
        assertTrue(Constants.HOUR_PILLARS.contains("甲子"))
        assertTrue(Constants.HOUR_PILLARS.contains("庚申"))
    }

    @Test
    fun testQuantumPhaseGeneration() {
        val timestamp = System.currentTimeMillis()
        val event = HealingEvent.create(1L, "测试", "7.83Hz")
        
        // 验证相位格式
        assertTrue(event.phase.contains("π/"))
        assertTrue(event.phase.contains("λ"))
        
        // 验证相位数值范围
        val parts = event.phase.split("π/", "λ")
        if (parts.size >= 2) {
            val phaseValue = parts[0].toIntOrNull()
            val lambdaValue = parts[1].toIntOrNull()
            
            assertNotNull(phaseValue)
            assertNotNull(lambdaValue)
            assertTrue("相位值应在0-313之间", phaseValue!! in 0..313)
            assertTrue("λ值应在0-59之间", lambdaValue!! in 0..59)
        }
    }

    @Test
    fun testHealingWaveGeneration() {
        val event = HealingEvent.create(1L, "测试", "7.83Hz")
        
        // 验证波形格式
        assertTrue(event.wave.startsWith("M42_"))
        assertTrue(event.wave.contains("Se_"))
        
        // 验证频率值提取
        val parts = event.wave.split("_")
        assertEquals(3, parts.size)
        assertEquals("M42", parts[0])
        assertEquals("7.83Se", parts[1])
        
        // 验证随机码
        val randomCode = parts[2].toIntOrNull()
        assertNotNull(randomCode)
        assertTrue("随机码应在1000-9999之间", randomCode!! in 1000..9999)
    }

    @Test
    fun testEnergyLevelRange() {
        // 测试多次生成确保能量等级在正确范围内
        repeat(100) {
            val event = HealingEvent.create(1L, "测试", "7.83Hz")
            assertTrue("能量等级应在80-100之间", event.energyLevel in 80..100)
        }
    }

    @Test
    fun testFormattedTimestamp() {
        val event = HealingEvent.create(1L, "测试", "7.83Hz")
        val formatted = event.getFormattedTimestamp()
        
        assertNotNull(formatted)
        assertTrue(formatted.matches(Regex("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3}")))
    }
}
