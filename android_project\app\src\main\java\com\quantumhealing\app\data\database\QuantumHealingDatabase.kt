package com.quantumhealing.app.data.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.quantumhealing.app.data.dao.HealingEventDao
import com.quantumhealing.app.data.dao.PersonDao
import com.quantumhealing.app.data.model.HealingEvent
import com.quantumhealing.app.data.model.Person

/**
 * 量子疗愈系统数据库
 */
@Database(
    entities = [Person::class, HealingEvent::class],
    version = 1,
    exportSchema = false
)
abstract class QuantumHealingDatabase : RoomDatabase() {
    
    abstract fun personDao(): PersonDao
    abstract fun healingEventDao(): HealingEventDao
    
    companion object {
        @Volatile
        private var INSTANCE: QuantumHealingDatabase? = null
        
        fun getDatabase(context: Context): QuantumHealingDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    QuantumHealingDatabase::class.java,
                    "quantum_healing_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
