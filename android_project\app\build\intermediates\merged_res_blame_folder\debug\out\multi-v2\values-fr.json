{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-33:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,321,419,549,634,700,797,880,946,1048,1123,1179,1258,1318,1372,1494,1553,1615,1669,1751,1886,1978,2062,2176,2255,2336,2429,2496,2562,2642,2723,2826,2899,2977,3050,3122,3215,3287,3379,3471,3545,3629,3721,3778,3844,3927,4014,4076,4140,4203,4305,4403,4500,4601", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "233,316,414,544,629,695,792,875,941,1043,1118,1174,1253,1313,1367,1489,1548,1610,1664,1746,1881,1973,2057,2171,2250,2331,2424,2491,2557,2637,2718,2821,2894,2972,3045,3117,3210,3282,3374,3466,3540,3624,3716,3773,3839,3922,4009,4071,4135,4198,4300,4398,4495,4596,4685"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3030,3113,3211,3341,3426,3492,3589,3672,3738,3840,3915,3971,4050,4110,4164,4286,4345,4407,4461,4543,4678,4770,4854,4968,5047,5128,5221,5288,5354,5434,5515,5618,5691,5769,5842,5914,6007,6079,6171,6263,6337,6421,6513,6570,6636,6719,6806,6868,6932,6995,7097,7195,7292,7393", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "283,3108,3206,3336,3421,3487,3584,3667,3733,3835,3910,3966,4045,4105,4159,4281,4340,4402,4456,4538,4673,4765,4849,4963,5042,5123,5216,5283,5349,5429,5510,5613,5686,5764,5837,5909,6002,6074,6166,6258,6332,6416,6508,6565,6631,6714,6801,6863,6927,6990,7092,7190,7287,7388,7477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d780b2c0a0ca762ae8fc4516db86618\\transformed\\appcompat-1.4.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "288,399,514,624,706,812,942,1020,1096,1187,1280,1378,1473,1573,1666,1759,1854,1945,2036,2122,2232,2343,2446,2557,2665,2772,2931,7482", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "394,509,619,701,807,937,1015,1091,1182,1275,1373,1468,1568,1661,1754,1849,1940,2031,2117,2227,2338,2441,2552,2660,2767,2926,3025,7564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c33bda6cfdbe9dfccb7561f3cbbee362\\transformed\\core-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7569", "endColumns": "100", "endOffsets": "7665"}}]}]}