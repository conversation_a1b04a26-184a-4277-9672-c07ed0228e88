R_DEF: Internal format may change without notice
local
color black
color purple_200
color purple_500
color purple_700
color quantum_accent
color quantum_primary
color quantum_primary_dark
color status_active
color status_healing
color status_inactive
color teal_200
color teal_700
color white
drawable ic_notification
id btnAdd
id btnDelete
id btnStartSystem
id btnUpdate
id etName
id rvPersons
id spinnerDay
id spinnerDisease
id spinnerGender
id spinnerHour
id spinnerHourPillar
id spinnerLocation
id spinnerMinute
id spinnerMonth
id spinnerYear
id spinnerYearPillar
id swActive
id tvBirthInfo
id tvDisease
id tvGender
id tvName
id tvStatus
id vStatusIndicator
layout activity_main
layout item_person
mipmap ic_launcher
mipmap ic_launcher_round
string add_person
string app_name
string birth_location
string birth_time
string cancel
string confirm_delete
string current_persons
string delete
string delete_person
string disease
string gender
string gregorian_birthday
string hour_pillar
string name
string name_exists
string name_required
string no_active_persons
string person_info_input
string person_list
string quantum_field_activated
string quantum_field_stopped
string quantum_healing_service
string ready
string service_description
string service_running
string start_system
string stop_system
string system_running
string update_person
string year_pillar
style Theme.QuantumHealing
xml backup_rules
xml data_extraction_rules
