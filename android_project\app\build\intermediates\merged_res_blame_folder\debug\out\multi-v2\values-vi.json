{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-33:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d780b2c0a0ca762ae8fc4516db86618\\transformed\\appcompat-1.4.2\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,377,479,588,672,775,894,972,1048,1139,1232,1327,1421,1521,1614,1709,1803,1894,1985,2069,2173,2281,2382,2487,2602,2707,2864,7222", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "372,474,583,667,770,889,967,1043,1134,1227,1322,1416,1516,1609,1704,1798,1889,1980,2064,2168,2276,2377,2482,2597,2702,2859,2958,7302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c33bda6cfdbe9dfccb7561f3cbbee362\\transformed\\core-1.7.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7307", "endColumns": "100", "endOffsets": "7403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,298,397,511,591,654,748,822,881,967,1028,1086,1150,1211,1265,1382,1439,1499,1553,1628,1755,1839,1917,2017,2101,2179,2270,2337,2403,2471,2547,2628,2707,2782,2855,2931,3020,3097,3188,3282,3356,3426,3519,3568,3634,3719,3805,3867,3931,3994,4093,4198,4296,4401", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,98,113,79,62,93,73,58,85,60,57,63,60,53,116,56,59,53,74,126,83,77,99,83,77,90,66,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,77", "endOffsets": "215,293,392,506,586,649,743,817,876,962,1023,1081,1145,1206,1260,1377,1434,1494,1548,1623,1750,1834,1912,2012,2096,2174,2265,2332,2398,2466,2542,2623,2702,2777,2850,2926,3015,3092,3183,3277,3351,3421,3514,3563,3629,3714,3800,3862,3926,3989,4088,4193,4291,4396,4474"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2963,3041,3140,3254,3334,3397,3491,3565,3624,3710,3771,3829,3893,3954,4008,4125,4182,4242,4296,4371,4498,4582,4660,4760,4844,4922,5013,5080,5146,5214,5290,5371,5450,5525,5598,5674,5763,5840,5931,6025,6099,6169,6262,6311,6377,6462,6548,6610,6674,6737,6836,6941,7039,7144", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,77,98,113,79,62,93,73,58,85,60,57,63,60,53,116,56,59,53,74,126,83,77,99,83,77,90,66,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,77", "endOffsets": "265,3036,3135,3249,3329,3392,3486,3560,3619,3705,3766,3824,3888,3949,4003,4120,4177,4237,4291,4366,4493,4577,4655,4755,4839,4917,5008,5075,5141,5209,5285,5366,5445,5520,5593,5669,5758,5835,5926,6020,6094,6164,6257,6306,6372,6457,6543,6605,6669,6732,6831,6936,7034,7139,7217"}}]}]}