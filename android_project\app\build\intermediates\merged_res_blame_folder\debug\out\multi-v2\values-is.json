{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-33:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d780b2c0a0ca762ae8fc4516db86618\\transformed\\appcompat-1.4.2\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "275,375,472,584,669,770,884,965,1044,1135,1228,1321,1415,1521,1614,1709,1804,1895,1989,2070,2180,2287,2384,2493,2593,2696,2851,7237", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "370,467,579,664,765,879,960,1039,1130,1223,1316,1410,1516,1609,1704,1799,1890,1984,2065,2175,2282,2379,2488,2588,2691,2846,2944,7313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,225,304,401,516,598,663,751,815,876,966,1029,1091,1159,1223,1279,1402,1467,1529,1585,1656,1783,1867,1951,2057,2134,2211,2298,2365,2431,2507,2587,2676,2743,2817,2887,2953,3039,3109,3200,3290,3364,3437,3526,3577,3649,3730,3816,3878,3942,4005,4119,4222,4330,4433", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,96,114,81,64,87,63,60,89,62,61,67,63,55,122,64,61,55,70,126,83,83,105,76,76,86,66,65,75,79,88,66,73,69,65,85,69,90,89,73,72,88,50,71,80,85,61,63,62,113,102,107,102,79", "endOffsets": "220,299,396,511,593,658,746,810,871,961,1024,1086,1154,1218,1274,1397,1462,1524,1580,1651,1778,1862,1946,2052,2129,2206,2293,2360,2426,2502,2582,2671,2738,2812,2882,2948,3034,3104,3195,3285,3359,3432,3521,3572,3644,3725,3811,3873,3937,4000,4114,4217,4325,4428,4508"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2949,3028,3125,3240,3322,3387,3475,3539,3600,3690,3753,3815,3883,3947,4003,4126,4191,4253,4309,4380,4507,4591,4675,4781,4858,4935,5022,5089,5155,5231,5311,5400,5467,5541,5611,5677,5763,5833,5924,6014,6088,6161,6250,6301,6373,6454,6540,6602,6666,6729,6843,6946,7054,7157", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,78,96,114,81,64,87,63,60,89,62,61,67,63,55,122,64,61,55,70,126,83,83,105,76,76,86,66,65,75,79,88,66,73,69,65,85,69,90,89,73,72,88,50,71,80,85,61,63,62,113,102,107,102,79", "endOffsets": "270,3023,3120,3235,3317,3382,3470,3534,3595,3685,3748,3810,3878,3942,3998,4121,4186,4248,4304,4375,4502,4586,4670,4776,4853,4930,5017,5084,5150,5226,5306,5395,5462,5536,5606,5672,5758,5828,5919,6009,6083,6156,6245,6296,6368,6449,6535,6597,6661,6724,6838,6941,7049,7152,7232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c33bda6cfdbe9dfccb7561f3cbbee362\\transformed\\core-1.7.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7318", "endColumns": "100", "endOffsets": "7414"}}]}]}