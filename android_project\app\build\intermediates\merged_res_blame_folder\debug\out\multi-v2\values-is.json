{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-38:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\151ecdfd05d4da12e68bb0fff234359b\\transformed\\material-1.9.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1016,1104,1168,1229,1319,1383,1446,1508,1576,1640,1696,1819,1884,1946,2002,2073,2200,2284,2368,2504,2581,2658,2745,2802,2857,2923,2999,3079,3168,3235,3309,3379,3445,3531,3601,3692,3782,3856,3929,4018,4069,4141,4222,4308,4370,4434,4497,4611,4714,4822,4925,4986,5045", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,86,56,54,65,75,79,88,66,73,69,65,85,69,90,89,73,72,88,50,71,80,85,61,63,62,113,102,107,102,60,58,79", "endOffsets": "265,339,411,490,572,652,749,864,946,1011,1099,1163,1224,1314,1378,1441,1503,1571,1635,1691,1814,1879,1941,1997,2068,2195,2279,2363,2499,2576,2653,2740,2797,2852,2918,2994,3074,3163,3230,3304,3374,3440,3526,3596,3687,3777,3851,3924,4013,4064,4136,4217,4303,4365,4429,4492,4606,4709,4817,4920,4981,5040,5120"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2994,3068,3140,3219,3301,4098,4195,4310,4392,4457,4545,4609,4670,4760,4824,4887,4949,5017,5081,5137,5260,5325,5387,5443,5514,5641,5725,5809,5945,6022,6099,6186,6243,6298,6364,6440,6520,6609,6676,6750,6820,6886,6972,7042,7133,7223,7297,7370,7459,7510,7582,7663,7749,7811,7875,7938,8052,8155,8263,8366,8427,8486", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,86,56,54,65,75,79,88,66,73,69,65,85,69,90,89,73,72,88,50,71,80,85,61,63,62,113,102,107,102,60,58,79", "endOffsets": "315,3063,3135,3214,3296,3376,4190,4305,4387,4452,4540,4604,4665,4755,4819,4882,4944,5012,5076,5132,5255,5320,5382,5438,5509,5636,5720,5804,5940,6017,6094,6181,6238,6293,6359,6435,6515,6604,6671,6745,6815,6881,6967,7037,7128,7218,7292,7365,7454,7505,7577,7658,7744,7806,7870,7933,8047,8150,8258,8361,8422,8481,8561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2303021cb7f3f78c4bc1b8231e5ae854\\transformed\\appcompat-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,420,517,629,714,815,929,1010,1089,1180,1273,1366,1460,1566,1659,1754,1849,1940,2034,2115,2225,2332,2429,2538,2638,2741,2896,8566", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "415,512,624,709,810,924,1005,1084,1175,1268,1361,1455,1561,1654,1749,1844,1935,2029,2110,2220,2327,2424,2533,2633,2736,2891,2989,8642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\031e7bcb191513255b93c6f8738cef28\\transformed\\core-1.10.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3381,3476,3583,3680,3780,3883,3987,8647", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3471,3578,3675,3775,3878,3982,4093,8743"}}]}]}