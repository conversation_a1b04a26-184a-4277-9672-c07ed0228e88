@echo off
:: 强制使用UTF-8编码并启用Unicode支持 
chcp 65001 >nul
powershell -command "Start-Process cmd -ArgumentList '/c chcp 65001 >nul && reg add HKCU\Console /v CodePage /t REG_DWORD /d 65001 /f' -Verb RunAs"
setlocal enableextensions enabledelayedexpansion 

title 🔍 Android 环境检测和自动修复工具
 
:: 设置控制台字体为支持Unicode的字体（仅对新版Windows有效）
if defined SYSTEMROOT (
    reg add "HKCU\Console\%CD%\%~nx0" /v "FaceName" /t REG_SZ /d "Lucida Console" /f >nul 2>&1 
    reg add "HKCU\Console\%CD%\%~nx0" /v "CodePage" /t REG_DWORD /d 65001 /f >nul 2>&1
)
 
:: 使用PowerShell输出Unicode字符（兼容性方案）
echo ⏳ 正在初始化...
powershell -noprofile -command "Write-Host '🔍 Android 环境检测和自动修复工具' -NoNewline"
echo.
echo =====================================
echo.
 
:: 检查Python环境 
set "PYTHON_CMD="
for %%P in (python py python3) do (
    %%P --version >nul 2>&1 && set "PYTHON_CMD=%%P" && goto :python_found 
)
 
:: 未找到Python的兼容性处理
echo ❌ 未找到Python，正在尝试系统路径...
for %%I in ("%SYSTEMROOT%\py.exe")  do (
    if exist "%%~I" (
        "%%~I" --version >nul 2>&1 && set "PYTHON_CMD=%%~I" && goto :python_found
    )
)
 
:: 完全找不到Python的情况 
echo ❌ 系统中未安装Python | find /v ""
echo 请安装Python 3.6+或手动检查环境 | find /v ""
echo.
echo 手动检查步骤： | find /v ""
echo 1. 打开Android Studio | find /v ""
echo 2. 进入Tools ^> SDK Manager | find /v ""
echo 3. 查看已安装的Android SDK版本 | find /v ""
echo 4. 告诉我您的最高API级别 | find /v ""
pause
exit /b 1
 
:python_found 
echo ✅ 找到Python(!PYTHON_CMD!)，开始环境检测... | find /v ""
echo.
 
:: 运行环境检测
"%PYTHON_CMD%" detect_environment.py  
 
:: 处理检测结果
if exist environment_config.json  (
    echo.
    echo 📋 正在根据检测结果自动修复项目配置... | find /v ""
    
    :: 运行修复脚本 
    "%PYTHON_CMD%" auto_fix_config.py  
    
    if !errorlevel! equ 0 (
        echo.
        echo ✅ 项目配置已自动修复！ | find /v ""
        echo.
        echo 🚀 下一步操作： | find /v ""
        echo 1. 在Android Studio中打开项目 | find /v ""
        echo 2. 点击 "Sync Project with Gradle Files" | find /v ""
        echo 3. 等待同步完成后尝试编译 | find /v ""
    ) else (
        echo ❌ 自动修复失败，请手动配置 | find /v ""
    )
) else (
    echo ❌ 环境检测失败，请手动检查配置 | find /v ""
)
 
echo.
echo 按任意键退出... | find /v ""
pause >nul 