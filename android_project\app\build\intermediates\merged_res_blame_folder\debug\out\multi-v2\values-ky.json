{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-35:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bea0caf4c4157f01bbd28088de62123\\transformed\\appcompat-1.5.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,381,490,602,687,792,909,988,1066,1157,1250,1345,1439,1539,1632,1727,1822,1913,2004,2085,2191,2296,2394,2501,2604,2719,2880,7321", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "376,485,597,682,787,904,983,1061,1152,1245,1340,1434,1534,1627,1722,1817,1908,1999,2080,2186,2291,2389,2496,2599,2714,2875,2977,7398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d0cd04beb3e7b9a1429c24c8d7d0af4\\transformed\\core-1.8.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7403", "endColumns": "100", "endOffsets": "7499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,305,406,547,631,695,789,859,920,1007,1071,1130,1204,1266,1320,1437,1495,1556,1610,1684,1806,1890,1986,2088,2166,2244,2333,2400,2466,2535,2612,2699,2771,2847,2929,3002,3087,3166,3256,3348,3422,3507,3597,3649,3714,3797,3882,3944,4008,4071,4188,4282,4382,4477", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,100,140,83,63,93,69,60,86,63,58,73,61,53,116,57,60,53,73,121,83,95,101,77,77,88,66,65,68,76,86,71,75,81,72,84,78,89,91,73,84,89,51,64,82,84,61,63,62,116,93,99,94,81", "endOffsets": "215,300,401,542,626,690,784,854,915,1002,1066,1125,1199,1261,1315,1432,1490,1551,1605,1679,1801,1885,1981,2083,2161,2239,2328,2395,2461,2530,2607,2694,2766,2842,2924,2997,3082,3161,3251,3343,3417,3502,3592,3644,3709,3792,3877,3939,4003,4066,4183,4277,4377,4472,4554"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2982,3067,3168,3309,3393,3457,3551,3621,3682,3769,3833,3892,3966,4028,4082,4199,4257,4318,4372,4446,4568,4652,4748,4850,4928,5006,5095,5162,5228,5297,5374,5461,5533,5609,5691,5764,5849,5928,6018,6110,6184,6269,6359,6411,6476,6559,6644,6706,6770,6833,6950,7044,7144,7239", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,84,100,140,83,63,93,69,60,86,63,58,73,61,53,116,57,60,53,73,121,83,95,101,77,77,88,66,65,68,76,86,71,75,81,72,84,78,89,91,73,84,89,51,64,82,84,61,63,62,116,93,99,94,81", "endOffsets": "265,3062,3163,3304,3388,3452,3546,3616,3677,3764,3828,3887,3961,4023,4077,4194,4252,4313,4367,4441,4563,4647,4743,4845,4923,5001,5090,5157,5223,5292,5369,5456,5528,5604,5686,5759,5844,5923,6013,6105,6179,6264,6354,6406,6471,6554,6639,6701,6765,6828,6945,7039,7139,7234,7316"}}]}]}