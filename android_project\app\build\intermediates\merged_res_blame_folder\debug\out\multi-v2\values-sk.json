{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-38:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\151ecdfd05d4da12e68bb0fff234359b\\transformed\\material-1.9.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1147,1246,1322,1387,1477,1541,1607,1661,1730,1790,1844,1961,2021,2083,2137,2209,2339,2426,2518,2657,2726,2804,2892,2946,2997,3063,3135,3212,3295,3367,3444,3517,3588,3676,3748,3840,3936,4010,4084,4180,4232,4299,4386,4473,4535,4599,4662,4768,4864,4962,5060,5118,5173", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,87,53,50,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,57,54,78", "endOffsets": "373,448,523,601,693,776,868,996,1077,1142,1241,1317,1382,1472,1536,1602,1656,1725,1785,1839,1956,2016,2078,2132,2204,2334,2421,2513,2652,2721,2799,2887,2941,2992,3058,3130,3207,3290,3362,3439,3512,3583,3671,3743,3835,3931,4005,4079,4175,4227,4294,4381,4468,4530,4594,4657,4763,4859,4957,5055,5113,5168,5247"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3140,3215,3290,3368,3460,4280,4372,4500,4581,4646,4745,4821,4886,4976,5040,5106,5160,5229,5289,5343,5460,5520,5582,5636,5708,5838,5925,6017,6156,6225,6303,6391,6445,6496,6562,6634,6711,6794,6866,6943,7016,7087,7175,7247,7339,7435,7509,7583,7679,7731,7798,7885,7972,8034,8098,8161,8267,8363,8461,8559,8617,8672", "endLines": "7,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,87,53,50,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,57,54,78", "endOffsets": "423,3210,3285,3363,3455,3538,4367,4495,4576,4641,4740,4816,4881,4971,5035,5101,5155,5224,5284,5338,5455,5515,5577,5631,5703,5833,5920,6012,6151,6220,6298,6386,6440,6491,6557,6629,6706,6789,6861,6938,7011,7082,7170,7242,7334,7430,7504,7578,7674,7726,7793,7880,7967,8029,8093,8156,8262,8358,8456,8554,8612,8667,8746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2303021cb7f3f78c4bc1b8231e5ae854\\transformed\\appcompat-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "428,535,636,747,833,941,1059,1138,1215,1306,1399,1497,1591,1691,1784,1879,1977,2068,2159,2243,2348,2456,2555,2661,2773,2876,3042,8751", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "530,631,742,828,936,1054,1133,1210,1301,1394,1492,1586,1686,1779,1874,1972,2063,2154,2238,2343,2451,2550,2656,2768,2871,3037,3135,8829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\031e7bcb191513255b93c6f8738cef28\\transformed\\core-1.10.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "40,41,42,43,44,45,46,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3543,3639,3741,3842,3940,4050,4158,8834", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3634,3736,3837,3935,4045,4153,4275,8930"}}]}]}