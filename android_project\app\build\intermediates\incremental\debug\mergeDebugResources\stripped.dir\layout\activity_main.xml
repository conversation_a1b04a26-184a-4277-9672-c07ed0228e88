<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- 左侧输入面板 -->
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginEnd="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="人员信息录入"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <!-- 姓名输入 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="姓名">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 性别选择 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="性别"
                        style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                        <AutoCompleteTextView
                            android:id="@+id/actvGender"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 公历生日 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="公历生日"
                        android:textSize="14sp"
                        android:layout_marginBottom="4dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="4dp"
                            android:hint="年"
                            style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                            <AutoCompleteTextView
                                android:id="@+id/actvYear"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="4dp"
                            android:hint="月"
                            style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                            <AutoCompleteTextView
                                android:id="@+id/actvMonth"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="4dp"
                            android:hint="日"
                            style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                            <AutoCompleteTextView
                                android:id="@+id/actvDay"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <!-- 出生时间 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="出生时间"
                        android:textSize="14sp"
                        android:layout_marginBottom="4dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="4dp"
                            android:hint="时"
                            style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                            <AutoCompleteTextView
                                android:id="@+id/actvHour"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="4dp"
                            android:hint="分"
                            style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                            <AutoCompleteTextView
                                android:id="@+id/actvMinute"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <!-- 年柱选择 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="年柱"
                        style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                        <AutoCompleteTextView
                            android:id="@+id/actvYearPillar"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 时柱选择 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="时柱"
                        style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                        <AutoCompleteTextView
                            android:id="@+id/actvHourPillar"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 出生地选择 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:hint="出生地"
                        style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                        <AutoCompleteTextView
                            android:id="@+id/actvLocation"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 疾病选择 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="疾病"
                        style="@style/Widget.MaterialComponents.TextInputLayout.ExposedDropdownMenu">

                        <AutoCompleteTextView
                            android:id="@+id/actvDisease"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 按钮区域 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnAdd"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:text="添加人员" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnUpdate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:text="更新人员"
                            android:enabled="false"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnDelete"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:text="删除人员"
                            android:enabled="false"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnStartSystem"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="启动系统"
                            style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </ScrollView>

    <!-- 右侧人员列表 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginStart="8dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="人员列表"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvPersons"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <!-- 状态栏 -->
            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="就绪 | 当前人员: 0"
                android:textSize="12sp"
                android:background="@color/design_default_color_surface"
                android:padding="8dp" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
