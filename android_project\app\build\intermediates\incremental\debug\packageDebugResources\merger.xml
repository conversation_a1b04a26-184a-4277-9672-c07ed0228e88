<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\xiangmu\liaoyu\android_project\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\xiangmu\liaoyu\android_project\app\src\main\res"><file name="ic_notification" path="H:\xiangmu\liaoyu\android_project\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="activity_main" path="H:\xiangmu\liaoyu\android_project\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="item_person" path="H:\xiangmu\liaoyu\android_project\app\src\main\res\layout\item_person.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="H:\xiangmu\liaoyu\android_project\app\src\main\res\mipmap\ic_launcher.png" qualifiers="" type="mipmap"/><file name="ic_launcher_round" path="H:\xiangmu\liaoyu\android_project\app\src\main\res\mipmap\ic_launcher_round.png" qualifiers="" type="mipmap"/><file path="H:\xiangmu\liaoyu\android_project\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="quantum_primary">#FF2196F3</color><color name="quantum_primary_dark">#FF1976D2</color><color name="quantum_accent">#FF00BCD4</color><color name="status_active">#FF4CAF50</color><color name="status_inactive">#FF9E9E9E</color><color name="status_healing">#FFFF9800</color></file><file path="H:\xiangmu\liaoyu\android_project\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">量子加持能量疗愈系统</string><string name="person_info_input">人员信息录入</string><string name="person_list">人员列表</string><string name="name">姓名</string><string name="gender">性别</string><string name="gregorian_birthday">公历生日</string><string name="birth_time">出生时间</string><string name="year_pillar">年柱</string><string name="hour_pillar">时柱</string><string name="birth_location">出生地</string><string name="disease">疾病</string><string name="add_person">添加人员</string><string name="update_person">更新人员</string><string name="delete_person">删除人员</string><string name="start_system">启动系统</string><string name="system_running">系统运行中</string><string name="stop_system">停止系统</string><string name="ready">就绪</string><string name="current_persons">当前人员: %d</string><string name="quantum_field_activated">量子疗愈场已激活</string><string name="quantum_field_stopped">量子疗愈场已停止</string><string name="name_required">姓名不能为空</string><string name="name_exists">该姓名已存在</string><string name="confirm_delete">确定要删除 %s 吗？</string><string name="delete">删除</string><string name="cancel">取消</string><string name="no_active_persons">请至少激活一名人员</string><string name="quantum_healing_service">量子疗愈服务</string><string name="service_running">量子疗愈系统运行中</string><string name="service_description">量子疗愈系统后台服务通知</string></file><file path="H:\xiangmu\liaoyu\android_project\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.QuantumHealing" parent="Theme.AppCompat.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/quantum_primary</item>
        <item name="colorPrimaryDark">@color/quantum_primary_dark</item>
        <item name="colorAccent">@color/quantum_accent</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryDark</item>
        
    </style></file><file name="backup_rules" path="H:\xiangmu\liaoyu\android_project\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="H:\xiangmu\liaoyu\android_project\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file path="H:\xiangmu\liaoyu\android_project\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.QuantumHealing" parent="Theme.AppCompat.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/quantum_accent</item>
        <item name="colorPrimaryDark">@color/quantum_primary_dark</item>
        <item name="colorAccent">@color/teal_200</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryDark</item>
        
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\xiangmu\liaoyu\android_project\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\xiangmu\liaoyu\android_project\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\xiangmu\liaoyu\android_project\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\xiangmu\liaoyu\android_project\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>