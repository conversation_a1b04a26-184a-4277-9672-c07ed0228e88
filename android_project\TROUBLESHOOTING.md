# 量子疗愈系统 - 问题解决指南

## 当前问题解决方案

### 问题：kotlin-kapt 插件未找到

**已修复的文件：**
1. `build.gradle` (根目录) - 更新了插件版本
2. `app/build.gradle` - 修正了插件引用
3. `gradle/wrapper/gradle-wrapper.properties` - 使用兼容的 Gradle 版本

### 解决步骤

#### 1. 确认 Android Studio 配置
- 确保使用 Android Studio Hedgehog (2023.1.1) 或更新版本
- 检查 JDK 版本：File → Project Structure → SDK Location → JDK Location
- 推荐使用 JDK 17

#### 2. 配置 Android SDK
1. 复制 `local.properties.template` 为 `local.properties`
2. 编辑 `local.properties`，设置正确的 SDK 路径：
   ```
   sdk.dir=C\:\\Users\\YourName\\AppData\\Local\\Android\\Sdk
   ```
   (替换为您实际的 Android SDK 路径)

#### 3. 清理和重新构建
在 Android Studio 中执行：
1. Build → Clean Project
2. File → Invalidate Caches and Restart
3. Build → Rebuild Project

#### 4. 如果仍有问题，手动同步
在项目根目录打开命令行：
```bash
gradlew clean
gradlew build
```

### 常见问题解决

#### 问题：Gradle 同步失败
**解决方案：**
1. 检查网络连接
2. 在 `gradle.properties` 中添加代理设置（如需要）：
   ```
   systemProp.http.proxyHost=your.proxy.host
   systemProp.http.proxyPort=8080
   systemProp.https.proxyHost=your.proxy.host
   systemProp.https.proxyPort=8080
   ```

#### 问题：Room 数据库编译错误
**解决方案：**
确保 kapt 插件正确配置，已在修复中包含。

#### 问题：依赖版本冲突
**解决方案：**
已降级到稳定版本，如仍有问题，可进一步降级：
```gradle
implementation 'androidx.room:room-runtime:2.4.3'
kapt 'androidx.room:room-compiler:2.4.3'
```

#### 问题：Kotlin 版本不兼容
**解决方案：**
确保所有 Kotlin 相关依赖使用相同版本 (1.9.10)。

### 验证修复

修复完成后，请验证：
1. Gradle 同步成功
2. 项目编译无错误
3. 应用可以正常运行

### 如果问题持续存在

1. **检查 Android Studio 版本**
   - 更新到最新稳定版本

2. **重新创建项目**
   - File → New → Import Project
   - 选择 `android_project` 文件夹

3. **手动配置 SDK**
   - File → Project Structure
   - 确认 Compile SDK Version: 34
   - 确认 Build Tools Version: 34.0.0

4. **清理 Gradle 缓存**
   ```bash
   # Windows
   rmdir /s %USERPROFILE%\.gradle\caches
   
   # macOS/Linux  
   rm -rf ~/.gradle/caches
   ```

### 联系支持

如果以上步骤都无法解决问题，请提供：
1. Android Studio 版本
2. JDK 版本
3. 操作系统版本
4. 完整的错误日志

---

**修复完成后，您的量子疗愈系统就可以正常运行了！** ✨
