package com.quantumhealing.app.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.quantumhealing.app.data.database.QuantumHealingDatabase
import com.quantumhealing.app.data.model.Person
import com.quantumhealing.app.repository.PersonRepository
import kotlinx.coroutines.launch

/**
 * 主界面ViewModel
 */
class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: PersonRepository
    val allPersons: LiveData<List<Person>>
    
    init {
        val database = QuantumHealingDatabase.getDatabase(application)
        repository = PersonRepository(database.personDao())
        allPersons = repository.allPersons
    }
    
    fun insertPerson(person: Person) = viewModelScope.launch {
        repository.insert(person)
    }
    
    fun updatePerson(person: Person) = viewModelScope.launch {
        repository.update(person)
    }
    
    fun deletePerson(person: Person) = viewModelScope.launch {
        repository.delete(person)
    }
    
    suspend fun getPersonByName(name: String): Person? {
        return repository.getPersonByName(name)
    }
    
    suspend fun getActivePersonCount(): Int {
        return repository.getActivePersonCount()
    }
}
