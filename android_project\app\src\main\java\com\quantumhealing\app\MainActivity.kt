package com.quantumhealing.app

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.quantumhealing.app.adapter.PersonAdapter
import com.quantumhealing.app.data.Constants
import com.quantumhealing.app.data.LocationMapping
import com.quantumhealing.app.data.model.Person
import com.quantumhealing.app.databinding.ActivityMainBinding
import com.quantumhealing.app.service.QuantumHealingService
import com.quantumhealing.app.viewmodel.MainViewModel
import kotlinx.coroutines.launch

/**
 * 主界面Activity
 * 对应原Python程序的PersonManager类
 */
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private val viewModel: MainViewModel by viewModels()
    private lateinit var personAdapter: PersonAdapter
    
    private var healingService: QuantumHealingService? = null
    private var isServiceBound = false
    private var selectedPerson: Person? = null

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as QuantumHealingService.LocalBinder
            healingService = binder.getService()
            isServiceBound = true
            
            // 设置服务监听器
            healingService?.setStatusListener { status ->
                runOnUiThread {
                    binding.tvStatus.text = status
                }
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            healingService = null
            isServiceBound = false
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupRecyclerView()
        setupObservers()
        bindService()
    }

    private fun setupUI() {
        // 设置下拉选择器
        setupDropdowns()
        
        // 设置按钮点击事件
        binding.btnAdd.setOnClickListener { addPerson() }
        binding.btnUpdate.setOnClickListener { updatePerson() }
        binding.btnDelete.setOnClickListener { deletePerson() }
        binding.btnStartSystem.setOnClickListener { toggleSystem() }
        
        // 初始化默认值
        setDefaultValues()
    }

    private fun setupDropdowns() {
        // 性别
        val genderAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, Constants.GENDERS)
        binding.actvGender.setAdapter(genderAdapter)
        
        // 年份 (1900-2025)
        val years = (1900..2025).map { it.toString() }
        val yearAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, years)
        binding.actvYear.setAdapter(yearAdapter)
        
        // 月份
        val months = (1..12).map { String.format("%02d", it) }
        val monthAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, months)
        binding.actvMonth.setAdapter(monthAdapter)
        
        // 日期
        val days = (1..31).map { String.format("%02d", it) }
        val dayAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, days)
        binding.actvDay.setAdapter(dayAdapter)
        
        // 小时
        val hours = (0..23).map { String.format("%02d", it) }
        val hourAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, hours)
        binding.actvHour.setAdapter(hourAdapter)
        
        // 分钟
        val minutes = (0..59).map { String.format("%02d", it) }
        val minuteAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, minutes)
        binding.actvMinute.setAdapter(minuteAdapter)
        
        // 年柱
        val yearPillarAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, Constants.YEAR_PILLARS)
        binding.actvYearPillar.setAdapter(yearPillarAdapter)
        
        // 时柱
        val hourPillarAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, Constants.HOUR_PILLARS)
        binding.actvHourPillar.setAdapter(hourPillarAdapter)
        
        // 出生地
        val locationAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, LocationMapping.LOCATIONS)
        binding.actvLocation.setAdapter(locationAdapter)
        
        // 疾病
        val diseaseAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, Constants.DISEASES)
        binding.actvDisease.setAdapter(diseaseAdapter)
    }

    private fun setDefaultValues() {
        binding.actvGender.setText(Constants.GENDERS[0], false)
        binding.actvYear.setText("2000", false)
        binding.actvMonth.setText("01", false)
        binding.actvDay.setText("01", false)
        binding.actvHour.setText("12", false)
        binding.actvMinute.setText("00", false)
        binding.actvYearPillar.setText(Constants.YEAR_PILLARS[0], false)
        binding.actvHourPillar.setText(Constants.HOUR_PILLARS[0], false)
        binding.actvLocation.setText(LocationMapping.LOCATIONS[0], false)
        binding.actvDisease.setText(Constants.DISEASES[0], false)
    }

    private fun setupRecyclerView() {
        personAdapter = PersonAdapter(
            onPersonClick = { person ->
                selectedPerson = person
                fillFormWithPerson(person)
                enableEditButtons(true)
            },
            onActiveToggle = { person, isActive ->
                togglePersonActive(person, isActive)
            }
        )
        
        binding.rvPersons.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = personAdapter
        }
    }

    private fun setupObservers() {
        viewModel.allPersons.observe(this) { persons ->
            personAdapter.submitList(persons)
            binding.tvStatus.text = "就绪 | 当前人员: ${persons.size}"
        }
    }

    private fun bindService() {
        val intent = Intent(this, QuantumHealingService::class.java)
        bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    private fun addPerson() {
        val person = getPersonFromForm()
        if (person == null) {
            Toast.makeText(this, "请填写完整信息", Toast.LENGTH_SHORT).show()
            return
        }

        lifecycleScope.launch {
            try {
                // 检查姓名是否已存在
                val existing = viewModel.getPersonByName(person.name)
                if (existing != null) {
                    Toast.makeText(this@MainActivity, "该姓名已存在", Toast.LENGTH_SHORT).show()
                    return@launch
                }

                viewModel.insertPerson(person)
                clearForm()
                Toast.makeText(this@MainActivity, "已添加: ${person.name}", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Toast.makeText(this@MainActivity, "添加失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun updatePerson() {
        val person = getPersonFromForm()
        val selected = selectedPerson
        if (person == null || selected == null) {
            Toast.makeText(this, "请选择要更新的人员", Toast.LENGTH_SHORT).show()
            return
        }

        lifecycleScope.launch {
            try {
                // 检查姓名冲突（除了当前人员）
                if (person.name != selected.name) {
                    val existing = viewModel.getPersonByName(person.name)
                    if (existing != null) {
                        Toast.makeText(this@MainActivity, "该姓名已存在", Toast.LENGTH_SHORT).show()
                        return@launch
                    }
                }

                val updatedPerson = person.copy(id = selected.id, isActive = selected.isActive)
                viewModel.updatePerson(updatedPerson)
                clearForm()
                Toast.makeText(this@MainActivity, "已更新: ${person.name}", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Toast.makeText(this@MainActivity, "更新失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun deletePerson() {
        val selected = selectedPerson ?: return

        AlertDialog.Builder(this)
            .setTitle("确认删除")
            .setMessage("确定要删除 ${selected.name} 吗？")
            .setPositiveButton("删除") { _, _ ->
                lifecycleScope.launch {
                    try {
                        viewModel.deletePerson(selected)
                        clearForm()
                        Toast.makeText(this@MainActivity, "已删除: ${selected.name}", Toast.LENGTH_SHORT).show()
                    } catch (e: Exception) {
                        Toast.makeText(this@MainActivity, "删除失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun toggleSystem() {
        if (healingService?.isRunning() == true) {
            healingService?.stopHealingSystem()
            binding.btnStartSystem.text = "启动系统"
        } else {
            lifecycleScope.launch {
                val activeCount = viewModel.getActivePersonCount()
                if (activeCount == 0) {
                    Toast.makeText(this@MainActivity, "请至少激活一名人员", Toast.LENGTH_SHORT).show()
                    return@launch
                }

                healingService?.startHealingSystem()
                binding.btnStartSystem.text = "系统运行中"
            }
        }
    }

    private fun togglePersonActive(person: Person, isActive: Boolean) {
        lifecycleScope.launch {
            val updatedPerson = person.copy(isActive = isActive)
            viewModel.updatePerson(updatedPerson)

            if (isActive) {
                healingService?.addPersonToHealing(updatedPerson)
            } else {
                healingService?.removePersonFromHealing(person.id, person.name)
            }
        }
    }

    private fun getPersonFromForm(): Person? {
        val name = binding.etName.text?.toString()?.trim()
        if (name.isNullOrEmpty()) return null

        val gender = binding.actvGender.text.toString()
        val year = binding.actvYear.text.toString()
        val month = binding.actvMonth.text.toString()
        val day = binding.actvDay.text.toString()
        val hour = binding.actvHour.text.toString()
        val minute = binding.actvMinute.text.toString()
        val yearPillar = binding.actvYearPillar.text.toString()
        val hourPillar = binding.actvHourPillar.text.toString()
        val location = binding.actvLocation.text.toString()
        val disease = binding.actvDisease.text.toString()

        val coordinate = LocationMapping.LOCATION_MAPPING[location] ?: "(0,0)"

        return Person(
            name = name,
            gender = gender,
            gregorianBirthday = "$year-$month-$day",
            approxBirthTime = "$hour:$minute",
            yearPillar = yearPillar,
            hourPillar = hourPillar,
            birthStargateCoord = coordinate,
            disease = disease
        )
    }

    private fun fillFormWithPerson(person: Person) {
        binding.etName.setText(person.name)
        binding.actvGender.setText(person.gender, false)

        val dateParts = person.gregorianBirthday.split("-")
        if (dateParts.size == 3) {
            binding.actvYear.setText(dateParts[0], false)
            binding.actvMonth.setText(dateParts[1], false)
            binding.actvDay.setText(dateParts[2], false)
        }

        val timeParts = person.approxBirthTime.split(":")
        if (timeParts.size == 2) {
            binding.actvHour.setText(timeParts[0], false)
            binding.actvMinute.setText(timeParts[1], false)
        }

        binding.actvYearPillar.setText(person.yearPillar, false)
        binding.actvHourPillar.setText(person.hourPillar, false)
        binding.actvDisease.setText(person.disease, false)

        // 查找地区名称
        val locationName = LocationMapping.LOCATION_MAPPING.entries
            .find { it.value == person.birthStargateCoord }?.key ?: ""
        binding.actvLocation.setText(locationName, false)
    }

    private fun clearForm() {
        binding.etName.text?.clear()
        setDefaultValues()
        selectedPerson = null
        personAdapter.clearSelection()
        enableEditButtons(false)
    }

    private fun enableEditButtons(enabled: Boolean) {
        binding.btnUpdate.isEnabled = enabled
        binding.btnDelete.isEnabled = enabled
    }

    override fun onDestroy() {
        super.onDestroy()
        if (isServiceBound) {
            unbindService(serviceConnection)
            isServiceBound = false
        }
    }
}
