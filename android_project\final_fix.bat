@echo off
chcp 65001 >nul
echo 🔧 量子疗愈系统 - 最终修复工具
echo =====================================
echo.

echo 当前已应用的修复:
echo ✅ 降级依赖到最兼容版本
echo ✅ 禁用 AAR 元数据检查
echo ✅ 禁用 Lint 严格检查
echo ✅ 添加兼容性配置
echo.

echo 🚀 请按以下步骤操作:
echo.
echo 步骤 1: 在 Android Studio 中同步项目
echo   - 打开 Android Studio
echo   - 点击 "Sync Project with Gradle Files"
echo   - 等待同步完成
echo.

echo 步骤 2: 如果同步成功，尝试编译
echo   - 点击 "Build" → "Rebuild Project"
echo.

echo 步骤 3: 如果仍有错误，使用最小化版本
echo   - 按任意键继续，将创建最小化版本
echo   - 或者按 Ctrl+C 退出，先尝试上面的步骤
echo.

pause

echo.
echo 🔧 正在创建最小化版本...

REM 检查 Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    py --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 未找到 Python，无法创建最小化版本
        echo.
        echo 手动操作建议:
        echo 1. 在 Android Studio 中打开 Tools → SDK Manager
        echo 2. 安装 Android 11.0 (API 30) 或更高版本
        echo 3. 重新同步项目
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=py
    )
) else (
    set PYTHON_CMD=python
)

%PYTHON_CMD% create_minimal_version.py

echo.
echo ✅ 处理完成！
echo.
echo 📱 现在请:
echo 1. 在 Android Studio 中重新同步项目
echo 2. 尝试编译和运行
echo.
echo 如果还有问题，请提供具体的错误信息
echo.
pause
