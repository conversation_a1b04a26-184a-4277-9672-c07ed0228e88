[{"merged": "com.quantumhealing.app-debug-40:/mipmap_ic_launcher_round.png.flat", "source": "com.quantumhealing.app-main-42:/mipmap/ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.quantumhealing.app-debug-40:\\drawable_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.quantumhealing.app-main-42:\\drawable\\ic_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.quantumhealing.app-debug-40:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.quantumhealing.app-main-42:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.quantumhealing.app-debug-40:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.quantumhealing.app-main-42:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.quantumhealing.app-debug-40:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.quantumhealing.app-main-42:\\xml\\backup_rules.xml"}, {"merged": "com.quantumhealing.app-debug-40:/mipmap_ic_launcher.png.flat", "source": "com.quantumhealing.app-main-42:/mipmap/ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.quantumhealing.app-debug-40:\\layout_item_person.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.quantumhealing.app-main-42:\\layout\\item_person.xml"}]