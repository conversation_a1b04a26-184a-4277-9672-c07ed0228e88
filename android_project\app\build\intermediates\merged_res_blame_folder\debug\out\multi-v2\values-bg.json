{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-35:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d0cd04beb3e7b9a1429c24c8d7d0af4\\transformed\\core-1.8.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7565", "endColumns": "100", "endOffsets": "7661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bea0caf4c4157f01bbd28088de62123\\transformed\\appcompat-1.5.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,390,496,601,687,797,918,998,1075,1166,1259,1354,1448,1548,1641,1736,1844,1935,2026,2109,2223,2331,2431,2545,2652,2760,2920,7481", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "385,491,596,682,792,913,993,1070,1161,1254,1349,1443,1543,1636,1731,1839,1930,2021,2104,2218,2326,2426,2540,2647,2755,2915,3014,7560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,303,398,531,620,686,783,863,925,1014,1079,1138,1211,1274,1328,1456,1513,1575,1629,1702,1845,1929,2017,2123,2211,2299,2384,2451,2517,2592,2668,2754,2831,2907,2984,3058,3149,3224,3315,3407,3481,3568,3659,3714,3780,3863,3949,4011,4075,4138,4255,4368,4479,4596", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,94,132,88,65,96,79,61,88,64,58,72,62,53,127,56,61,53,72,142,83,87,105,87,87,84,66,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,85", "endOffsets": "215,298,393,526,615,681,778,858,920,1009,1074,1133,1206,1269,1323,1451,1508,1570,1624,1697,1840,1924,2012,2118,2206,2294,2379,2446,2512,2587,2663,2749,2826,2902,2979,3053,3144,3219,3310,3402,3476,3563,3654,3709,3775,3858,3944,4006,4070,4133,4250,4363,4474,4591,4677"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3019,3102,3197,3330,3419,3485,3582,3662,3724,3813,3878,3937,4010,4073,4127,4255,4312,4374,4428,4501,4644,4728,4816,4922,5010,5098,5183,5250,5316,5391,5467,5553,5630,5706,5783,5857,5948,6023,6114,6206,6280,6367,6458,6513,6579,6662,6748,6810,6874,6937,7054,7167,7278,7395", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,82,94,132,88,65,96,79,61,88,64,58,72,62,53,127,56,61,53,72,142,83,87,105,87,87,84,66,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,85", "endOffsets": "265,3097,3192,3325,3414,3480,3577,3657,3719,3808,3873,3932,4005,4068,4122,4250,4307,4369,4423,4496,4639,4723,4811,4917,5005,5093,5178,5245,5311,5386,5462,5548,5625,5701,5778,5852,5943,6018,6109,6201,6275,6362,6453,6508,6574,6657,6743,6805,6869,6932,7049,7162,7273,7390,7476"}}]}]}