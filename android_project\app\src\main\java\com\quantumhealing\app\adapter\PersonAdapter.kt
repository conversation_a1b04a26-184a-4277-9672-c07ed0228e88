package com.quantumhealing.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.switchmaterial.SwitchMaterial
import com.quantumhealing.app.R
import com.quantumhealing.app.data.model.Person

/**
 * 人员列表适配器
 */
class PersonAdapter(
    private val onPersonClick: (Person) -> Unit,
    private val onActiveToggle: (Person, Boolean) -> Unit
) : ListAdapter<Person, PersonAdapter.PersonViewHolder>(PersonDiffCallback()) {

    private var selectedPosition = -1

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PersonViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_person, parent, false)
        return PersonViewHolder(view)
    }

    override fun onBindViewHolder(holder: PersonViewHolder, position: Int) {
        val person = getItem(position)
        holder.bind(person, position == selectedPosition)
    }

    fun setSelectedPosition(position: Int) {
        val oldPosition = selectedPosition
        selectedPosition = position
        if (oldPosition != -1) {
            notifyItemChanged(oldPosition)
        }
        if (selectedPosition != -1) {
            notifyItemChanged(selectedPosition)
        }
    }

    fun clearSelection() {
        val oldPosition = selectedPosition
        selectedPosition = -1
        if (oldPosition != -1) {
            notifyItemChanged(oldPosition)
        }
    }

    inner class PersonViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val vStatusIndicator: View = itemView.findViewById(R.id.vStatusIndicator)
        private val tvName: TextView = itemView.findViewById(R.id.tvName)
        private val tvGender: TextView = itemView.findViewById(R.id.tvGender)
        private val tvDisease: TextView = itemView.findViewById(R.id.tvDisease)
        private val tvBirthInfo: TextView = itemView.findViewById(R.id.tvBirthInfo)
        private val swActive: SwitchMaterial = itemView.findViewById(R.id.swActive)

        fun bind(person: Person, isSelected: Boolean) {
            tvName.text = person.name
            tvGender.text = person.gender
            tvDisease.text = person.disease
            tvBirthInfo.text = "${person.gregorianBirthday} ${person.approxBirthTime}"

            // 设置激活状态
            swActive.isChecked = person.isActive
            swActive.setOnCheckedChangeListener { _, isChecked ->
                onActiveToggle(person, isChecked)
            }

            // 设置状态指示器颜色
            val statusColor = when {
                person.isActive -> ContextCompat.getColor(itemView.context, android.R.color.holo_green_light)
                else -> ContextCompat.getColor(itemView.context, android.R.color.darker_gray)
            }
            vStatusIndicator.setBackgroundColor(statusColor)

            // 设置选中状态
            val backgroundColor = if (isSelected) {
                ContextCompat.getColor(itemView.context, R.color.design_default_color_primary_variant)
            } else {
                ContextCompat.getColor(itemView.context, android.R.color.white)
            }
            itemView.setBackgroundColor(backgroundColor)

            // 设置点击事件
            itemView.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    setSelectedPosition(position)
                    onPersonClick(person)
                }
            }
        }
    }

    class PersonDiffCallback : DiffUtil.ItemCallback<Person>() {
        override fun areItemsTheSame(oldItem: Person, newItem: Person): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Person, newItem: Person): Boolean {
            return oldItem == newItem
        }
    }
}
