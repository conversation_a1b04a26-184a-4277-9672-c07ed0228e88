# 量子加持能量疗愈系统 - 部署指南

## 快速开始

### 1. 环境准备

#### 必需软件
- **Android Studio**: Hedgehog | 2023.1.1 或更高版本
- **JDK**: OpenJDK 17 或更高版本
- **Android SDK**: API 34 (Android 14)
- **Gradle**: 8.2 或更高版本

#### 推荐配置
- **内存**: 8GB RAM 或更高
- **存储**: 至少 10GB 可用空间
- **操作系统**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+

### 2. 项目导入

```bash
# 克隆项目（如果从版本控制获取）
git clone <repository-url>
cd android_project

# 或直接使用提供的项目文件夹
cd android_project
```

### 3. Android Studio 配置

1. **打开项目**
   - 启动 Android Studio
   - 选择 "Open an Existing Project"
   - 导航到 `android_project` 文件夹
   - 点击 "OK"

2. **等待同步**
   - Gradle 会自动开始同步
   - 首次同步可能需要 5-10 分钟
   - 确保网络连接稳定

3. **SDK 检查**
   - 打开 Tools → SDK Manager
   - 确保已安装 Android 14 (API 34)
   - 安装必要的 build tools

### 4. 编译配置

#### Gradle 配置检查
确保 `gradle.properties` 包含以下配置：
```properties
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
android.useAndroidX=true
kotlin.code.style=official
android.nonTransitiveRClass=true
```

#### 依赖版本验证
检查 `app/build.gradle` 中的关键依赖：
```gradle
dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.room:room-runtime:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'
}
```

### 5. 设备准备

#### 真机调试
1. **开启开发者选项**
   - 设置 → 关于手机 → 连续点击版本号 7 次
   - 返回设置 → 开发者选项

2. **启用 USB 调试**
   - 开发者选项 → USB 调试 → 开启
   - 连接电脑时选择"传输文件"模式

3. **安装驱动**（Windows）
   - 下载对应手机品牌的 USB 驱动
   - 或使用 Android Studio 的通用驱动

#### 模拟器配置
1. **创建 AVD**
   - Tools → AVD Manager → Create Virtual Device
   - 选择 Pixel 6 或更新设备
   - 系统镜像：Android 14 (API 34)
   - RAM: 至少 2GB

2. **性能优化**
   - 启用硬件加速 (HAXM/Hyper-V)
   - 分配足够的 RAM 和存储空间

### 6. 编译运行

#### 方法一：Android Studio
1. 连接设备或启动模拟器
2. 点击工具栏的 "Run" 按钮 (绿色三角形)
3. 选择目标设备
4. 等待编译和安装完成

#### 方法二：命令行
```bash
# 编译 Debug 版本
./gradlew assembleDebug

# 安装到连接的设备
./gradlew installDebug

# 编译并运行
./gradlew installDebug && adb shell am start -n com.quantumhealing.app/.MainActivity
```

### 7. 发布版本

#### 生成签名密钥
```bash
keytool -genkey -v -keystore quantum-healing.keystore -alias quantum -keyalg RSA -keysize 2048 -validity 10000
```

#### 配置签名
在 `app/build.gradle` 中添加：
```gradle
android {
    signingConfigs {
        release {
            storeFile file('quantum-healing.keystore')
            storePassword 'your_store_password'
            keyAlias 'quantum'
            keyPassword 'your_key_password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

#### 生成 APK
```bash
# 生成发布版 APK
./gradlew assembleRelease

# APK 位置：app/build/outputs/apk/release/app-release.apk
```

### 8. 测试验证

#### 单元测试
```bash
./gradlew test
```

#### 功能测试清单
- [ ] 添加人员信息
- [ ] 编辑人员信息
- [ ] 删除人员
- [ ] 激活/停用人员
- [ ] 启动疗愈系统
- [ ] 后台服务运行
- [ ] 通知显示
- [ ] 数据持久化

#### 性能测试
- [ ] 内存使用 < 100MB
- [ ] CPU 使用 < 20%
- [ ] 电池消耗合理
- [ ] 长时间运行稳定

### 9. 常见问题

#### 编译错误
```
Q: Gradle sync failed
A: 检查网络连接，清理项目缓存：Build → Clean Project

Q: Room database error
A: 确保已添加 kapt 插件和 Room 依赖

Q: Kotlin version conflict
A: 统一项目中的 Kotlin 版本
```

#### 运行时错误
```
Q: 应用崩溃
A: 检查 Logcat 输出，确认权限配置

Q: 后台服务被杀死
A: 在设置中将应用加入白名单

Q: 数据库初始化失败
A: 检查存储权限，清除应用数据重试
```

#### 性能问题
```
Q: 内存泄漏
A: 使用 LeakCanary 检测，确保正确释放资源

Q: 电池消耗过高
A: 优化后台任务频率，使用 Doze 模式适配
```

### 10. 部署检查清单

#### 编译前检查
- [ ] 代码无语法错误
- [ ] 所有依赖版本兼容
- [ ] 权限配置正确
- [ ] 资源文件完整

#### 测试检查
- [ ] 单元测试通过
- [ ] 功能测试完成
- [ ] 性能测试达标
- [ ] 兼容性测试通过

#### 发布前检查
- [ ] 签名配置正确
- [ ] 版本号更新
- [ ] 混淆配置优化
- [ ] 安装包大小合理

### 11. 技术支持

如遇到部署问题，请检查：
1. Android Studio 版本是否最新
2. SDK 和 build tools 是否完整
3. 设备系统版本是否支持
4. 网络连接是否稳定

---

**祝您部署顺利！量子疗愈系统期待为您服务** ✨
