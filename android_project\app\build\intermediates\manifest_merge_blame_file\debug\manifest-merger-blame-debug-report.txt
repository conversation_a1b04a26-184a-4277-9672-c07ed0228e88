1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.quantumhealing.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="30" />
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
12-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:6:5-77
12-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:7:5-68
13-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:7:22-65
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:8:5-66
14-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
15-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:9:5-77
15-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:9:22-74
16
17    <application
17-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:11:5-40:19
18        android:allowBackup="true"
18-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:12:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d0cd04beb3e7b9a1429c24c8d7d0af4\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:13:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="true"
23        android:fullBackupContent="@xml/backup_rules"
23-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:14:9-54
24        android:icon="@mipmap/ic_launcher"
24-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:15:9-43
25        android:label="@string/app_name"
25-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:16:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:17:9-54
27        android:supportsRtl="true"
27-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:18:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.QuantumHealing" >
29-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:19:9-52
30
31        <!-- 主Activity -->
32        <activity
32-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:23:9-31:20
33            android:name="com.quantumhealing.app.MainActivity"
33-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:24:13-41
34            android:exported="true"
34-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:25:13-36
35            android:theme="@style/Theme.QuantumHealing" >
35-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:26:13-56
36            <intent-filter>
36-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:27:13-30:29
37                <action android:name="android.intent.action.MAIN" />
37-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:28:17-69
37-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:28:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:29:17-77
39-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:29:27-74
40            </intent-filter>
41        </activity>
42
43        <!-- 量子疗愈后台服务 -->
44        <service
44-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:34:9-38:58
45            android:name="com.quantumhealing.app.service.QuantumHealingService"
45-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:35:13-58
46            android:enabled="true"
46-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:36:13-35
47            android:exported="false"
47-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:37:13-37
48            android:foregroundServiceType="specialUse" />
48-->H:\xiangmu\liaoyu\android_project\app\src\main\AndroidManifest.xml:38:13-55
49        <service
49-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:25:9-28:40
50            android:name="androidx.room.MultiInstanceInvalidationService"
50-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:26:13-74
51            android:directBootAware="true"
51-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:27:13-43
52            android:exported="false" />
52-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c23c1cc71c6220e758da346ca73ae433\transformed\room-runtime-2.4.3\AndroidManifest.xml:28:13-37
53
54        <provider
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
56            android:authorities="com.quantumhealing.app.androidx-startup"
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.emoji2.text.EmojiCompatInitializer"
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
60                android:value="androidx.startup" />
60-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba52cbd3723cf662cdfc624d34e7809f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
62                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
62-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
63                android:value="androidx.startup" />
63-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\01acc56b3fe7b8dceb6e674562d70585\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
64        </provider>
65    </application>
66
67</manifest>
