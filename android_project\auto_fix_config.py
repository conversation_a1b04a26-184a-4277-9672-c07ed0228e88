#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动修复 Android 项目配置
根据环境检测结果自动调整 build.gradle 配置
"""

import json
import os
import re
from pathlib import Path

class AndroidConfigFixer:
    def __init__(self, config_file="environment_config.json"):
        self.config_file = config_file
        self.environment_info = None
        self.load_environment_config()
    
    def load_environment_config(self):
        """加载环境配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.environment_info = json.load(f)
            print(f"✅ 已加载环境配置: {self.config_file}")
        except FileNotFoundError:
            print(f"❌ 未找到配置文件: {self.config_file}")
            return False
        except json.JSONDecodeError:
            print(f"❌ 配置文件格式错误: {self.config_file}")
            return False
        return True
    
    def fix_app_build_gradle(self):
        """修复 app/build.gradle 文件"""
        build_gradle_path = "app/build.gradle"
        
        if not os.path.exists(build_gradle_path):
            print(f"❌ 未找到文件: {build_gradle_path}")
            return False
        
        try:
            with open(build_gradle_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 获取推荐配置
            rec_config = self.environment_info.get('recommended_config', {})
            compile_sdk = rec_config.get('compileSdk', 30)
            target_sdk = rec_config.get('targetSdk', 30)
            dependencies = rec_config.get('dependencies', {})
            
            print(f"🔧 修复配置: compileSdk={compile_sdk}, targetSdk={target_sdk}")
            
            # 修复 compileSdk
            content = re.sub(
                r'compileSdk\s+\d+',
                f'compileSdk {compile_sdk}',
                content
            )
            
            # 修复 targetSdk
            content = re.sub(
                r'targetSdk\s+\d+',
                f'targetSdk {target_sdk}',
                content
            )
            
            # 修复依赖版本
            if dependencies:
                # Core KTX
                if 'core_ktx' in dependencies:
                    content = re.sub(
                        r"implementation\s+['\"]androidx\.core:core-ktx:[^'\"]+['\"]",
                        f"implementation 'androidx.core:core-ktx:{dependencies['core_ktx']}'",
                        content
                    )
                
                # AppCompat
                if 'appcompat' in dependencies:
                    content = re.sub(
                        r"implementation\s+['\"]androidx\.appcompat:appcompat:[^'\"]+['\"]",
                        f"implementation 'androidx.appcompat:appcompat:{dependencies['appcompat']}'",
                        content
                    )
                
                # Material Design
                if 'material' in dependencies:
                    content = re.sub(
                        r"implementation\s+['\"]com\.google\.android\.material:material:[^'\"]+['\"]",
                        f"implementation 'com.google.android.material:material:{dependencies['material']}'",
                        content
                    )
                
                # Lifecycle
                if 'lifecycle' in dependencies:
                    lifecycle_version = dependencies['lifecycle']
                    content = re.sub(
                        r"implementation\s+['\"]androidx\.lifecycle:lifecycle-viewmodel-ktx:[^'\"]+['\"]",
                        f"implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:{lifecycle_version}'",
                        content
                    )
                    content = re.sub(
                        r"implementation\s+['\"]androidx\.lifecycle:lifecycle-livedata-ktx:[^'\"]+['\"]",
                        f"implementation 'androidx.lifecycle:lifecycle-livedata-ktx:{lifecycle_version}'",
                        content
                    )
                    content = re.sub(
                        r"implementation\s+['\"]androidx\.lifecycle:lifecycle-service:[^'\"]+['\"]",
                        f"implementation 'androidx.lifecycle:lifecycle-service:{lifecycle_version}'",
                        content
                    )
                
                # Room
                if 'room' in dependencies:
                    room_version = dependencies['room']
                    content = re.sub(
                        r"implementation\s+['\"]androidx\.room:room-runtime:[^'\"]+['\"]",
                        f"implementation 'androidx.room:room-runtime:{room_version}'",
                        content
                    )
                    content = re.sub(
                        r"implementation\s+['\"]androidx\.room:room-ktx:[^'\"]+['\"]",
                        f"implementation 'androidx.room:room-ktx:{room_version}'",
                        content
                    )
                    content = re.sub(
                        r"kapt\s+['\"]androidx\.room:room-compiler:[^'\"]+['\"]",
                        f"kapt 'androidx.room:room-compiler:{room_version}'",
                        content
                    )
            
            # 写回文件
            with open(build_gradle_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已修复: {build_gradle_path}")
            return True
            
        except Exception as e:
            print(f"❌ 修复失败: {e}")
            return False
    
    def create_local_properties(self):
        """创建 local.properties 文件"""
        android_sdk = self.environment_info.get('android_sdk')
        if not android_sdk:
            print("⚠️  未找到 Android SDK 路径，跳过 local.properties 创建")
            return False
        
        local_properties_path = "local.properties"
        
        # 转换路径格式（Windows 需要转义反斜杠）
        if os.name == 'nt':  # Windows
            sdk_path = android_sdk.replace('\\', '\\\\')
        else:
            sdk_path = android_sdk
        
        content = f"# This file was automatically generated by environment detector\n"
        content += f"# Location of the Android SDK\n"
        content += f"sdk.dir={sdk_path}\n"
        
        try:
            with open(local_properties_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已创建: {local_properties_path}")
            print(f"   SDK 路径: {android_sdk}")
            return True
        except Exception as e:
            print(f"❌ 创建 local.properties 失败: {e}")
            return False
    
    def print_fix_summary(self):
        """打印修复摘要"""
        rec_config = self.environment_info.get('recommended_config', {})
        
        print("\n" + "="*50)
        print("🔧 自动修复摘要")
        print("="*50)
        
        print(f"📱 目标 Android API: {rec_config.get('compileSdk', 'N/A')}")
        print(f"🎯 目标 SDK 版本: {rec_config.get('targetSdk', 'N/A')}")
        
        dependencies = rec_config.get('dependencies', {})
        if dependencies:
            print(f"\n📦 依赖版本:")
            for key, version in dependencies.items():
                print(f"   {key}: {version}")
        
        available_apis = self.environment_info.get('available_apis', [])
        if available_apis:
            print(f"\n✅ 您的系统支持的 API 级别: {', '.join(map(str, available_apis))}")
        
        print("\n" + "="*50)
    
    def run_fix(self):
        """运行完整的修复流程"""
        if not self.environment_info:
            print("❌ 无法加载环境配置，修复失败")
            return False
        
        print("🔧 开始自动修复项目配置...")
        
        success = True
        
        # 修复 build.gradle
        if not self.fix_app_build_gradle():
            success = False
        
        # 创建 local.properties
        if not self.create_local_properties():
            print("⚠️  local.properties 创建失败，但不影响编译")
        
        # 打印摘要
        self.print_fix_summary()
        
        if success:
            print("\n✅ 自动修复完成！")
            print("\n🚀 下一步:")
            print("1. 在 Android Studio 中打开项目")
            print("2. 点击 'Sync Project with Gradle Files'")
            print("3. 等待同步完成")
            print("4. 尝试编译项目")
        else:
            print("\n❌ 修复过程中出现错误")
            print("请手动检查配置或联系技术支持")
        
        return success

def main():
    fixer = AndroidConfigFixer()
    return fixer.run_fix()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
