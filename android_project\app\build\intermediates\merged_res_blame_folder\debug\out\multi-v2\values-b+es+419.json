{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-35:/values-b+es+419/values-b+es+419.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-b+es+419\\values-b+es+419.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,319,421,549,630,695,790,860,923,1016,1088,1151,1225,1289,1345,1463,1521,1583,1639,1719,1853,1942,2023,2134,2215,2295,2385,2452,2518,2594,2676,2764,2837,2914,2984,3061,3150,3224,3318,3420,3492,3573,3677,3730,3797,3890,3979,4041,4105,4168,4279,4376,4478,4576", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,101,127,80,64,94,69,62,92,71,62,73,63,55,117,57,61,55,79,133,88,80,110,80,79,89,66,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,82", "endOffsets": "228,314,416,544,625,690,785,855,918,1011,1083,1146,1220,1284,1340,1458,1516,1578,1634,1714,1848,1937,2018,2129,2210,2290,2380,2447,2513,2589,2671,2759,2832,2909,2979,3056,3145,3219,3313,3415,3487,3568,3672,3725,3792,3885,3974,4036,4100,4163,4274,4371,4473,4571,4654"}}]}]}