{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-33:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c33bda6cfdbe9dfccb7561f3cbbee362\\transformed\\core-1.7.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7348", "endColumns": "100", "endOffsets": "7444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,307,399,511,593,657,752,822,885,992,1059,1120,1187,1249,1303,1417,1476,1537,1591,1666,1792,1880,1969,2081,2153,2226,2315,2382,2448,2519,2596,2682,2754,2830,2911,2981,3068,3140,3231,3324,3398,3473,3565,3617,3683,3767,3853,3915,3979,4042,4146,4246,4340,4441", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,91,111,81,63,94,69,62,106,66,60,66,61,53,113,58,60,53,74,125,87,88,111,71,72,88,66,65,70,76,85,71,75,80,69,86,71,90,92,73,74,91,51,65,83,85,61,63,62,103,99,93,100,83", "endOffsets": "223,302,394,506,588,652,747,817,880,987,1054,1115,1182,1244,1298,1412,1471,1532,1586,1661,1787,1875,1964,2076,2148,2221,2310,2377,2443,2514,2591,2677,2749,2825,2906,2976,3063,3135,3226,3319,3393,3468,3560,3612,3678,3762,3848,3910,3974,4037,4141,4241,4335,4436,4520"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2969,3048,3140,3252,3334,3398,3493,3563,3626,3733,3800,3861,3928,3990,4044,4158,4217,4278,4332,4407,4533,4621,4710,4822,4894,4967,5056,5123,5189,5260,5337,5423,5495,5571,5652,5722,5809,5881,5972,6065,6139,6214,6306,6358,6424,6508,6594,6656,6720,6783,6887,6987,7081,7182", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,78,91,111,81,63,94,69,62,106,66,60,66,61,53,113,58,60,53,74,125,87,88,111,71,72,88,66,65,70,76,85,71,75,80,69,86,71,90,92,73,74,91,51,65,83,85,61,63,62,103,99,93,100,83", "endOffsets": "273,3043,3135,3247,3329,3393,3488,3558,3621,3728,3795,3856,3923,3985,4039,4153,4212,4273,4327,4402,4528,4616,4705,4817,4889,4962,5051,5118,5184,5255,5332,5418,5490,5566,5647,5717,5804,5876,5967,6060,6134,6209,6301,6353,6419,6503,6589,6651,6715,6778,6882,6982,7076,7177,7261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d780b2c0a0ca762ae8fc4516db86618\\transformed\\appcompat-1.4.2\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,386,491,601,686,792,911,991,1068,1159,1252,1347,1441,1541,1634,1729,1826,1917,2008,2089,2194,2297,2395,2502,2608,2708,2874,7266", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "381,486,596,681,787,906,986,1063,1154,1247,1342,1436,1536,1629,1724,1821,1912,2003,2084,2189,2292,2390,2497,2603,2703,2869,2964,7343"}}]}]}