{"logs": [{"outputFile": "com.quantumhealing.app-mergeDebugResources-33:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c33bda6cfdbe9dfccb7561f3cbbee362\\transformed\\core-1.7.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7369", "endColumns": "100", "endOffsets": "7465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d780b2c0a0ca762ae8fc4516db86618\\transformed\\appcompat-1.4.2\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "284,395,500,608,695,799,910,989,1067,1158,1251,1346,1440,1538,1631,1726,1820,1911,2002,2082,2194,2302,2399,2508,2612,2719,2878,7288", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "390,495,603,690,794,905,984,1062,1153,1246,1341,1435,1533,1626,1721,1815,1906,1997,2077,2189,2297,2394,2503,2607,2714,2873,2974,7364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4e112358a03acfa0a8eb6e946ce96973\\transformed\\material-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,321,424,540,623,688,781,846,905,992,1054,1114,1180,1242,1296,1404,1461,1522,1577,1648,1768,1859,1945,2063,2149,2235,2323,2390,2456,2527,2605,2688,2761,2837,2910,2981,3073,3146,3236,3329,3403,3474,3565,3617,3685,3769,3854,3916,3980,4043,4147,4253,4349,4457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,102,115,82,64,92,64,58,86,61,59,65,61,53,107,56,60,54,70,119,90,85,117,85,85,87,66,65,70,77,82,72,75,72,70,91,72,89,92,73,70,90,51,67,83,84,61,63,62,103,105,95,107,85", "endOffsets": "229,316,419,535,618,683,776,841,900,987,1049,1109,1175,1237,1291,1399,1456,1517,1572,1643,1763,1854,1940,2058,2144,2230,2318,2385,2451,2522,2600,2683,2756,2832,2905,2976,3068,3141,3231,3324,3398,3469,3560,3612,3680,3764,3849,3911,3975,4038,4142,4248,4344,4452,4538"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2979,3066,3169,3285,3368,3433,3526,3591,3650,3737,3799,3859,3925,3987,4041,4149,4206,4267,4322,4393,4513,4604,4690,4808,4894,4980,5068,5135,5201,5272,5350,5433,5506,5582,5655,5726,5818,5891,5981,6074,6148,6219,6310,6362,6430,6514,6599,6661,6725,6788,6892,6998,7094,7202", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,86,102,115,82,64,92,64,58,86,61,59,65,61,53,107,56,60,54,70,119,90,85,117,85,85,87,66,65,70,77,82,72,75,72,70,91,72,89,92,73,70,90,51,67,83,84,61,63,62,103,105,95,107,85", "endOffsets": "279,3061,3164,3280,3363,3428,3521,3586,3645,3732,3794,3854,3920,3982,4036,4144,4201,4262,4317,4388,4508,4599,4685,4803,4889,4975,5063,5130,5196,5267,5345,5428,5501,5577,5650,5721,5813,5886,5976,6069,6143,6214,6305,6357,6425,6509,6594,6656,6720,6783,6887,6993,7089,7197,7283"}}]}]}