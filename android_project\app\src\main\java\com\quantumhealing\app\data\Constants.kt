package com.quantumhealing.app.data

/**
 * 量子疗愈系统常量定义
 * 包含疾病频率库、地理坐标映射、干支等数据
 */
object Constants {
    
    // 量子共振频率库 (基于Schumann共振与生物节律)
    val HEALING_FREQUENCIES = mapOf(
        // 通用
        "通用" to listOf("7.83Hz", "14.3Hz", "20.14Hz"),
        
        // 重大系统性疾病
        "癌症（恶性肿瘤）" to listOf("7.83Hz", "152Hz", "20.14Hz"),
        "心脏病" to listOf("7.83Hz", "20.14Hz", "33.33Hz"),
        "艾滋病" to listOf("7.83Hz", "40.68Hz", "33.33Hz"),
        "系统性红斑狼疮" to listOf("7.83Hz", "14.3Hz", "40.68Hz"),
        "系统性硬皮病（硬皮病）" to listOf("7.83Hz", "20.14Hz", "14.3Hz"),
        
        // 神经系统疾病
        "癫痫" to listOf("7.83Hz", "33.33Hz", "14.3Hz"),
        "帕金森病" to listOf("7.83Hz", "11.79Hz", "40.68Hz"),
        "重症肌无力" to listOf("7.83Hz", "20.14Hz", "33.33Hz"),
        "多发性硬化" to listOf("7.83Hz", "14.3Hz", "26.4Hz"),
        
        // 代谢性疾病
        "糖尿病" to listOf("7.83Hz", "40.68Hz", "152Hz"),
        "痛风" to listOf("7.83Hz", "33.33Hz", "14.3Hz"),
        "甲状腺功能减退症" to listOf("7.83Hz", "20.14Hz", "40.68Hz"),
        "甲亢（甲亢性心脏病）" to listOf("7.83Hz", "14.3Hz", "33.33Hz"),
        
        // 免疫性疾病
        "类风湿性关节炎" to listOf("7.83Hz", "40.68Hz", "14.3Hz"),
        "强直性脊柱炎" to listOf("7.83Hz", "26.4Hz", "20.14Hz"),
        "自身免疫性肝炎" to listOf("7.83Hz", "33.33Hz", "14.3Hz"),
        
        // 心脑血管疾病
        "冠心病" to listOf("7.83Hz", "20.14Hz", "14.3Hz"),
        "脑梗塞" to listOf("7.83Hz", "33.33Hz", "40.68Hz"),
        "高血压" to listOf("7.83Hz", "14.3Hz", "20.14Hz"),
        
        // 呼吸系统疾病
        "哮喘" to listOf("7.83Hz", "40.68Hz", "33.33Hz"),
        "矽肺" to listOf("7.83Hz", "20.14Hz", "152Hz"),
        "肺纤维化" to listOf("7.83Hz", "14.3Hz", "40.68Hz"),
        
        // 消化系统疾病
        "肝硬化" to listOf("7.83Hz", "33.33Hz", "14.3Hz"),
        "溃疡性结肠炎" to listOf("7.83Hz", "40.68Hz", "20.14Hz"),
        "慢性胰腺炎" to listOf("7.83Hz", "14.3Hz", "33.33Hz"),
        
        // 血液疾病
        "白血病" to listOf("7.83Hz", "152Hz", "40.68Hz"),
        "再生障碍性贫血" to listOf("7.83Hz", "20.14Hz", "33.33Hz"),
        "血小板减少性紫癜" to listOf("7.83Hz", "14.3Hz", "40.68Hz"),
        
        // 特殊病症
        "烧伤烫伤" to listOf("7.83Hz", "40.68Hz", "152Hz"),
        "先天性疾病" to listOf("7.83Hz", "33.33Hz", "20.14Hz"),
        "精神分裂症" to listOf("7.83Hz", "14.3Hz", "26.4Hz")
    )
    
    // 年柱
    val YEAR_PILLARS = listOf(
        "甲子", "乙丑", "丙寅", "丁卯", "戊辰", "己巳", "庚午", "辛未", "壬申", "癸酉",
        "甲戌", "乙亥", "丙子", "丁丑", "戊寅", "己卯", "庚辰", "辛巳", "壬午", "癸未",
        "甲申", "乙酉", "丙戌", "丁亥", "戊子", "己丑", "庚寅", "辛卯", "壬辰", "癸巳",
        "甲午", "乙未", "丙申", "丁酉", "戊戌", "己亥", "庚子", "辛丑", "壬寅", "癸卯",
        "甲辰", "乙巳", "丙午", "丁未", "戊申", "己酉", "庚戌", "辛亥", "壬子", "癸丑",
        "甲寅", "乙卯", "丙辰", "丁巳", "戊午", "己未", "庚申", "辛酉", "壬戌", "癸亥"
    )
    
    // 时柱
    val HOUR_PILLARS = listOf(
        "甲子", "乙丑", "丙寅", "癸酉", "甲戌", "乙亥", "丁卯", "戊辰", "己巳", "庚午",
        "辛未", "壬申", "丙子", "丁丑", "戊寅", "乙酉", "丙戌", "丁亥", "己卯", "庚辰",
        "辛巳", "壬午", "癸未", "甲申", "戊子", "己丑", "庚寅", "丁酉", "戊戌", "己亥",
        "辛卯", "壬辰", "癸巳", "甲午", "乙未", "丙申", "庚子", "辛丑", "壬寅", "己酉",
        "庚戌", "辛亥", "癸卯", "甲辰", "乙巳", "丙午", "丁未", "戊申", "壬子", "癸丑",
        "甲寅", "辛酉", "壬戌", "癸亥", "乙卯", "丙辰", "丁巳", "戊午", "己未", "庚申"
    )
    
    val GENDERS = listOf("男", "女")
    
    // 获取所有疾病列表
    val DISEASES = HEALING_FREQUENCIES.keys.toList()
}
